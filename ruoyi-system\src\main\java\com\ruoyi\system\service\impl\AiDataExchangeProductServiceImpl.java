package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeProductMapper;
import com.ruoyi.system.domain.AiDataExchangeProduct;
import com.ruoyi.system.service.IAiDataExchangeProductService;

/**
 * AI数据交换的产品信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeProductServiceImpl implements IAiDataExchangeProductService 
{
    @Autowired
    private AiDataExchangeProductMapper aiDataExchangeProductMapper;

    /**
     * 查询AI数据交换的产品信息
     * 
     * @param aiDataExchangeProductId AI数据交换的产品信息主键
     * @return AI数据交换的产品信息
     */
    @Override
    public AiDataExchangeProduct selectAiDataExchangeProductByAiDataExchangeProductId(Long aiDataExchangeProductId)
    {
        return aiDataExchangeProductMapper.selectAiDataExchangeProductByAiDataExchangeProductId(aiDataExchangeProductId);
    }

    /**
     * 查询AI数据交换的产品信息列表
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return AI数据交换的产品信息
     */
    @Override
    public List<AiDataExchangeProduct> selectAiDataExchangeProductList(AiDataExchangeProduct aiDataExchangeProduct)
    {
        return aiDataExchangeProductMapper.selectAiDataExchangeProductList(aiDataExchangeProduct);
    }

    /**
     * 新增AI数据交换的产品信息
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeProduct(AiDataExchangeProduct aiDataExchangeProduct)
    {
        return aiDataExchangeProductMapper.insertAiDataExchangeProduct(aiDataExchangeProduct);
    }

    /**
     * 修改AI数据交换的产品信息
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeProduct(AiDataExchangeProduct aiDataExchangeProduct)
    {
        return aiDataExchangeProductMapper.updateAiDataExchangeProduct(aiDataExchangeProduct);
    }

    /**
     * 批量删除AI数据交换的产品信息
     * 
     * @param aiDataExchangeProductIds 需要删除的AI数据交换的产品信息主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeProductByAiDataExchangeProductIds(Long[] aiDataExchangeProductIds)
    {
        return aiDataExchangeProductMapper.deleteAiDataExchangeProductByAiDataExchangeProductIds(aiDataExchangeProductIds);
    }

    /**
     * 删除AI数据交换的产品信息信息
     * 
     * @param aiDataExchangeProductId AI数据交换的产品信息主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeProductByAiDataExchangeProductId(Long aiDataExchangeProductId)
    {
        return aiDataExchangeProductMapper.deleteAiDataExchangeProductByAiDataExchangeProductId(aiDataExchangeProductId);
    }
}
