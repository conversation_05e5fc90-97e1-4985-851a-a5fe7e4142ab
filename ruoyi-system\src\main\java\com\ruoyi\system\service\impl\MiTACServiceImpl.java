package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.system.domain.mitac.MiTACQualityReportRequest;
import com.ruoyi.system.domain.mitac.MiTACQualityReportResponse;
import com.ruoyi.system.domain.mitac.MiTACTokenRequest;
import com.ruoyi.system.domain.mitac.MiTACTokenResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.net.ssl.*;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.cert.X509Certificate;

/**
 * MiTAC服务实现类
 */
@Service
public class MiTACServiceImpl {
    private static final Logger log = LoggerFactory.getLogger(MiTACServiceImpl.class);

    // MiTAC API基础URL
    private static final String BASE_URL = "https://mslproductapitest.mic.com.cn:8433";
    // 获取Token的URL
    private static final String TOKEN_URL = BASE_URL + "/api/Auth/GetToken";
    // 接收质检报告的URL
    private static final String QUALITY_REPORT_URL = BASE_URL + "/api/Quality/RcvVendorMatOQAReport";

    // 固定的客户端ID和密钥
    private static final String CLIENT_ID = "F7D18580-24CF-466E-9D5F-BC5AA6732B1E";
    private static final String SECRET_KEY = "IDdwg5fu4OAA/SWWpq4pvQ==";

    // 代理服务器配置 - 与Postman相同的代理设置
    private static final String PROXY_HOST = "**************";
    private static final int PROXY_PORT = 3128;

    /**
     * 服务初始化时配置SSL和代理
     */
    @PostConstruct
    public void init() {
        log.info("初始化MiTAC服务...");
        trustAllCertificates();
        configureProxy();
        log.info("MiTAC服务初始化完成");
    }

    /**
     * 配置系统代理
     */
    private void configureProxy() {
        try {
            // 设置系统代理属性
            System.setProperty("http.proxyHost", PROXY_HOST);
            System.setProperty("http.proxyPort", String.valueOf(PROXY_PORT));
            System.setProperty("https.proxyHost", PROXY_HOST);
            System.setProperty("https.proxyPort", String.valueOf(PROXY_PORT));
            log.info("已配置系统代理: {}:{}", PROXY_HOST, PROXY_PORT);
        } catch (Exception e) {
            log.error("配置系统代理失败", e);
        }
    }

    /**
     * 获取MiTAC API的访问令牌
     *
     * @return 访问令牌
     */
    public String getToken() {
        try {
            MiTACTokenRequest request = new MiTACTokenRequest(CLIENT_ID, SECRET_KEY);
            String requestJson = JSON.toJSONString(request);

            log.info("获取MiTAC Token请求: {}", requestJson);
            log.info("使用URL: {}", TOKEN_URL);

            // 使用自定义方法发送请求，确保使用代理
            String responseJson = sendPostRequest(TOKEN_URL, requestJson, MediaType.APPLICATION_JSON_VALUE, null);
            log.info("获取MiTAC Token响应: {}", responseJson);

            MiTACTokenResponse response = JSON.parseObject(responseJson, MiTACTokenResponse.class);
            if (response.getSuccess() && response.getData() != null) {
                return response.getData().getToken();
            } else {
                log.error("获取MiTAC Token失败: {}", response.getMsg());
                throw new RuntimeException("获取MiTAC Token失败: " + response.getMsg());
            }
        } catch (Exception e) {
            log.error("获取MiTAC Token异常", e);
            throw new RuntimeException("获取MiTAC Token异常: " + e.getMessage());
        }
    }

    /**
     * 信任所有SSL证书
     */
    private void trustAllCertificates() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };

            // 创建SSLContext并使用我们的TrustManager
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 创建HostnameVerifier，接受所有主机名
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // 设置默认的HostnameVerifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            log.info("已配置信任所有SSL证书");
        } catch (Exception e) {
            log.error("配置SSL信任失败", e);
        }
    }

    /**
     * 发送质检报告到MiTAC
     *
     * @param request 质检报告请求
     * @return 质检报告响应
     */
    public MiTACQualityReportResponse sendQualityReport(MiTACQualityReportRequest request) {
        try {
            // 获取Token
            String token = getToken();
//            String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJDbGllbnQiOiJGT1hDT05OIiwiQ2xpZW50SWQiOiJGN0QxODU4MC0yNENGLTQ2NkUtOUQ1Ri1CQzVBQTY3MzJCMUUiLCJTZWNyZXRLZXkiOiJJRGR3ZzVmdTRPQUEvU1dXcHE0cHZRPT0iLCJTY29wZSI6IltcIlF1YWxpdHlcIl0iLCJSb2xlIjoibnVsbCIsIkV4cCI6IuS6lOaciCDpgLHlm5sgMDggMjAyNSAxMDowNDowNCDkuIrljYgiLCJuYmYiOjE3NDY2NjYyNDQsImV4cCI6MTc0NjY2OTg0NCwiaXNzIjoiaHR0cHM6Ly9uZXh0c2Zjcy10ZXN0Lm1pYy5jb20udHciLCJhdWQiOiJodHRwczovL25leHRzZmNzLXRlc3QubWljLmNvbS50dyJ9.zy_e3ECcZCO-NSb3E8z7W42aAkZyzt2BovQKXQCSzpY";

            // 准备请求
            String requestJson = JSON.toJSONString(request);
            log.info("发送质检报告请求: {}", requestJson);
            log.info("使用URL: {}", QUALITY_REPORT_URL);

            // 发送请求
            String responseJson = sendPostRequest(QUALITY_REPORT_URL, requestJson, MediaType.APPLICATION_JSON_VALUE, token);
            log.info("发送质检报告响应: {}", responseJson);

            // 解析响应
            MiTACQualityReportResponse response = JSON.parseObject(responseJson, MiTACQualityReportResponse.class);

            // 检查响应是否成功
            if (response != null && !response.getSuccess()) {
                // 如果响应不成功，但有错误信息，直接返回错误信息
                log.warn("MiTAC API返回错误: {}", response.getMsg());
                return response; // 返回错误响应，让调用者处理
            }

            return response;
        } catch (RuntimeException e) {
            // 直接将RuntimeException中的消息传递给调用者
            log.error("发送质检报告异常: {}", e.getMessage());

            // 创建一个错误响应对象
            MiTACQualityReportResponse errorResponse = new MiTACQualityReportResponse();
            errorResponse.setSuccess(false);
            errorResponse.setCode(400); // 假设是客户端错误
            errorResponse.setMsg(e.getMessage()); // 使用异常消息

            return errorResponse; // 返回包含错误信息的响应对象
        } catch (Exception e) {
            log.error("发送质检报告异常", e);

            // 创建一个错误响应对象
            MiTACQualityReportResponse errorResponse = new MiTACQualityReportResponse();
            errorResponse.setSuccess(false);
            errorResponse.setCode(500); // 服务器错误
            errorResponse.setMsg("发送质检报告异常: " + e.getMessage());

            return errorResponse; // 返回包含错误信息的响应对象
        }
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param requestJson 请求JSON
     * @param contentType 内容类型
     * @param token 访问令牌（可选）
     * @return 响应JSON
     */
    private String sendPostRequest(String url, String requestJson, String contentType, String token) {
        HttpsURLConnection conn = null;
        try {
            // 创建代理对象
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(PROXY_HOST, PROXY_PORT));

            // 设置连接超时
            int timeout = 30000; // 30秒

            java.net.URL realUrl = new java.net.URL(url);
            conn = (HttpsURLConnection) realUrl.openConnection(proxy);
            conn.setConnectTimeout(timeout);
            conn.setReadTimeout(timeout);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", contentType);
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");

            // 如果有token，添加Authorization头
            if (token != null && !token.isEmpty()) {
                conn.setRequestProperty("Authorization", "Bearer " + token);
            }

            conn.setDoOutput(true);
            conn.setDoInput(true);

            // 发送请求
            try (java.io.OutputStream os = conn.getOutputStream()) {
                byte[] input = requestJson.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应
            StringBuilder response = new StringBuilder();
            try (java.io.BufferedReader br = new java.io.BufferedReader(
                    new java.io.InputStreamReader(conn.getInputStream(), "utf-8"))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            }

            return response.toString();
        } catch (java.io.IOException e) {
            // 处理HTTP错误响应
            if (conn != null) {
                try {
                    int responseCode = conn.getResponseCode();
                    log.error("HTTP错误: {} {}", responseCode, conn.getResponseMessage());

                    // 读取错误响应内容
                    StringBuilder errorResponse = new StringBuilder();
                    try (java.io.BufferedReader br = new java.io.BufferedReader(
                            new java.io.InputStreamReader(conn.getErrorStream(), "utf-8"))) {
                        String line;
                        while ((line = br.readLine()) != null) {
                            errorResponse.append(line);
                        }
                    }

                    String errorContent = errorResponse.toString();
                    log.error("错误响应内容: {}", errorContent);

                    // 如果是400错误，尝试解析错误内容
                    if (responseCode == 400 && !errorContent.isEmpty()) {
                        try {
                            // 尝试解析JSON错误响应
                            MiTACQualityReportResponse errorObj = JSON.parseObject(errorContent, MiTACQualityReportResponse.class);
                            if (errorObj != null && !errorObj.getSuccess() && errorObj.getMsg() != null) {
                                throw new RuntimeException(errorObj.getMsg());
                            }
                        } catch (Exception jsonEx) {
                            // JSON解析失败，返回原始错误内容
                            throw new RuntimeException("MiTAC API错误: " + errorContent);
                        }
                    }

                    // 如果没有特殊处理，返回原始错误
                    return errorContent;
                } catch (Exception ex) {
                    // 如果是我们自己抛出的带有错误信息的异常，直接抛出
                    if (ex instanceof RuntimeException) {
                        throw (RuntimeException) ex;
                    }
                    log.error("处理错误响应异常", ex);
                    throw new RuntimeException("处理错误响应异常: " + ex.getMessage());
                }
            }

            log.error("发送POST请求异常: {}", e.getMessage(), e);
            throw new RuntimeException("发送POST请求异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("发送POST请求异常: {}", e.getMessage(), e);
            throw new RuntimeException("发送POST请求异常: " + e.getMessage());
        }
    }
}
