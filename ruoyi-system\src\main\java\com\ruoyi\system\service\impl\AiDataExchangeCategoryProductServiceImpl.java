package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeCategoryProductMapper;
import com.ruoyi.system.domain.AiDataExchangeCategoryProduct;
import com.ruoyi.system.domain.AiDataExchangeProduct;
import com.ruoyi.system.service.IAiDataExchangeCategoryProductService;

/**
 * AI数据交换的产品类别和产品的关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeCategoryProductServiceImpl implements IAiDataExchangeCategoryProductService
{
    @Autowired
    private AiDataExchangeCategoryProductMapper aiDataExchangeCategoryProductMapper;

    /**
     * 查询AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryId AI数据交换的产品类别ID
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @return AI数据交换的产品类别和产品的关联
     */
    @Override
    public AiDataExchangeCategoryProduct selectAiDataExchangeCategoryProductByIds(Long aiDataExchangeCategoryId, Long aiDataExchangeProductId)
    {
        return aiDataExchangeCategoryProductMapper.selectAiDataExchangeCategoryProductByIds(aiDataExchangeCategoryId, aiDataExchangeProductId);
    }

    /**
     * 查询AI数据交换的产品类别和产品的关联列表
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return AI数据交换的产品类别和产品的关联
     */
    @Override
    public List<AiDataExchangeCategoryProduct> selectAiDataExchangeCategoryProductList(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct)
    {
        return aiDataExchangeCategoryProductMapper.selectAiDataExchangeCategoryProductList(aiDataExchangeCategoryProduct);
    }

    /**
     * 新增AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeCategoryProduct(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct)
    {
        return aiDataExchangeCategoryProductMapper.insertAiDataExchangeCategoryProduct(aiDataExchangeCategoryProduct);
    }

    /**
     * 修改AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeCategoryProduct(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct)
    {
        return aiDataExchangeCategoryProductMapper.updateAiDataExchangeCategoryProduct(aiDataExchangeCategoryProduct);
    }

    /**
     * 批量删除AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryIds 需要删除的AI数据交换的产品类别ID集合
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeCategoryProductByCategoryIds(Long[] aiDataExchangeCategoryIds)
    {
        return aiDataExchangeCategoryProductMapper.deleteAiDataExchangeCategoryProductByCategoryIds(aiDataExchangeCategoryIds);
    }

    /**
     * 批量删除AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeProductIds 需要删除的AI数据交换的产品ID集合
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeCategoryProductByProductIds(Long[] aiDataExchangeProductIds)
    {
        return aiDataExchangeCategoryProductMapper.deleteAiDataExchangeCategoryProductByProductIds(aiDataExchangeProductIds);
    }

    /**
     * 删除AI数据交换的产品类别和产品的关联信息
     *
     * @param aiDataExchangeCategoryId AI数据交换的产品类别ID
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeCategoryProductByIds(Long aiDataExchangeCategoryId, Long aiDataExchangeProductId)
    {
        return aiDataExchangeCategoryProductMapper.deleteAiDataExchangeCategoryProductByIds(aiDataExchangeCategoryId, aiDataExchangeProductId);
    }

    /**
     * 根据产品类别ID查询产品信息
     *
     * @param categoryId 产品类别ID
     * @return 产品信息列表
     */
    @Override
    public List<AiDataExchangeProduct> selectProductsByCategoryId(Long categoryId)
    {
        return aiDataExchangeCategoryProductMapper.selectProductsByCategoryId(categoryId);
    }
}
