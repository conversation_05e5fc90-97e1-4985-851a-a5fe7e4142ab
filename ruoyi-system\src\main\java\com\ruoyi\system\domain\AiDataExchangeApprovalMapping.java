package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 签核人员映射对象 ai_data_exchange_approval_mapping
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeApprovalMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联的maker料号ID */
    @Excel(name = "关联的maker料号ID")
    private Long aiDataExchangeMakerPartNoId;

    /** 签核人ID */
    @Excel(name = "签核人ID")
    private Long userId;

    /** 签核类型（復核/核定），注意為繁體 */
    @Excel(name = "签核类型")
    private String approverType;

    public void setAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId) 
    {
        this.aiDataExchangeMakerPartNoId = aiDataExchangeMakerPartNoId;
    }

    public Long getAiDataExchangeMakerPartNoId() 
    {
        return aiDataExchangeMakerPartNoId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setApproverType(String approverType) 
    {
        this.approverType = approverType;
    }

    public String getApproverType() 
    {
        return approverType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeMakerPartNoId", getAiDataExchangeMakerPartNoId())
            .append("userId", getUserId())
            .append("approverType", getApproverType())
            .toString();
    }
}
