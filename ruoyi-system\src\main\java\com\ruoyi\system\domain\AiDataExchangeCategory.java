package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换的产品类别对象 ai_data_exchange_category
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换的产品类别ID */
    private Long aiDataExchangeCategoryId;

    /** 产品类别名 */
    @Excel(name = "产品类别名")
    private String categoryName;

    public void setAiDataExchangeCategoryId(Long aiDataExchangeCategoryId) 
    {
        this.aiDataExchangeCategoryId = aiDataExchangeCategoryId;
    }

    public Long getAiDataExchangeCategoryId() 
    {
        return aiDataExchangeCategoryId;
    }

    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeCategoryId", getAiDataExchangeCategoryId())
            .append("categoryName", getCategoryName())
            .toString();
    }
}
