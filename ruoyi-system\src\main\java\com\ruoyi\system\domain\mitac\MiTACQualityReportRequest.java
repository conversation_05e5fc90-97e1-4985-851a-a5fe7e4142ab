package com.ruoyi.system.domain.mitac;

import java.util.List;

/**
 * MiTAC质检报告请求参数
 */
public class MiTACQualityReportRequest {
    private String vendorCode;
    private String companyName;
    private String reportName;
    private String partNo;
    private String makerPartNo;
    private String customer;
    private String rev;
    private String category;
    private String description;
    private Integer lotSize;
    private SampleSize SampleSize;
    private String dateCode;
    private String orderNo;
    private String asnNo;
    private String result;
    private String approvedBy;
    private String checkedBy;
    private String inspectedBy;
    private List<Inspection> inspection;

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getPartNo() {
        return partNo;
    }

    public void setPartNo(String partNo) {
        this.partNo = partNo;
    }

    public String getMakerPartNo() {
        return makerPartNo;
    }

    public void setMakerPartNo(String makerPartNo) {
        this.makerPartNo = makerPartNo;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getRev() {
        return rev;
    }

    public void setRev(String rev) {
        this.rev = rev;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getLotSize() {
        return lotSize;
    }

    public void setLotSize(Integer lotSize) {
        this.lotSize = lotSize;
    }

    public SampleSize getSampleSize() {
        return SampleSize;
    }

    public void setSampleSize(SampleSize sampleSize) {
        this.SampleSize = sampleSize;
    }

    public String getDateCode() {
        return dateCode;
    }

    public void setDateCode(String dateCode) {
        this.dateCode = dateCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getCheckedBy() {
        return checkedBy;
    }

    public void setCheckedBy(String checkedBy) {
        this.checkedBy = checkedBy;
    }

    public String getInspectedBy() {
        return inspectedBy;
    }

    public void setInspectedBy(String inspectedBy) {
        this.inspectedBy = inspectedBy;
    }

    public List<Inspection> getInspection() {
        return inspection;
    }

    public void setInspection(List<Inspection> inspection) {
        this.inspection = inspection;
    }

    public static class SampleSize {
        private Integer Dimension;
        private Integer Appearance;

        public Integer getDimension() {
            return Dimension;
        }

        public void setDimension(Integer dimension) {
            Dimension = dimension;
        }

        public Integer getAppearance() {
            return Appearance;
        }

        public void setAppearance(Integer appearance) {
            Appearance = appearance;
        }
    }

    public static class Inspection {
        private String category;
        private List<Item> items;

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public List<Item> getItems() {
            return items;
        }

        public void setItems(List<Item> items) {
            this.items = items;
        }
    }

    public static class Item {
        private String item;
        private String spec;
        private String result;

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getSpec() {
            return spec;
        }

        public void setSpec(String spec) {
            this.spec = spec;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }
    }
}
