package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换的产品与maker料号的关联对象 ai_data_exchange_product_maker_part_no
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeProductMakerPartNo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换的产品ID */
    @Excel(name = "产品ID")
    private Long aiDataExchangeProductId;

    /** AI数据交换的maker料号ID */
    @Excel(name = "maker料号ID")
    private Long aiDataExchangeMakerPartNoId;

    public void setAiDataExchangeProductId(Long aiDataExchangeProductId) 
    {
        this.aiDataExchangeProductId = aiDataExchangeProductId;
    }

    public Long getAiDataExchangeProductId() 
    {
        return aiDataExchangeProductId;
    }

    public void setAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId) 
    {
        this.aiDataExchangeMakerPartNoId = aiDataExchangeMakerPartNoId;
    }

    public Long getAiDataExchangeMakerPartNoId() 
    {
        return aiDataExchangeMakerPartNoId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeProductId", getAiDataExchangeProductId())
            .append("aiDataExchangeMakerPartNoId", getAiDataExchangeMakerPartNoId())
            .toString();
    }
}
