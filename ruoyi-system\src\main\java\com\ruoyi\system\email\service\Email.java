package com.ruoyi.system.email.service;

import java.io.IOException;

public interface Email {
    String selectEmail(String email,String subject,String body,String cc) throws IOException;
    String sendEmail() throws IOException;
    String sendCCEmail(String emailByCC,String subjectByCC,String bodyByCC,String ccByCC) throws IOException;

    String sendEmailBySparePart(String toEmails, String ccEmails, String subject, String content)  throws IOException;
}
