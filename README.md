# Quality Assurance Backend System

## 项目简介
Quality Assurance Backend System 是一个基于RuoYi框架开发的质量保证管理系统。该系统采用前后端分离架构，后端使用Spring Boot框架开发，提供完整的质量保证管理功能。

## 技术架构
- 后端框架：Spring Boot 2.5.15
- 安全框架：Spring Security 5.7.12
- 持久层框架：MyBatis
- 数据库连接池：Druid
- 缓存框架：Redis
- 定时任务：Quartz
- 代码生成：Velocity
- 接口文档：Swagger3
- 日志框架：Logback

## 系统模块
系统分为以下模块：
1. ruoyi-admin：后台管理模块，系统入口
2. ruoyi-framework：核心框架模块
3. ruoyi-system：系统管理模块
4. ruoyi-quartz：定时任务模块
5. ruoyi-generator：代码生成模块
6. ruoyi-common：公共工具模块

## 核心功能
1. 用户管理
   - 用户信息管理
   - 角色管理
   - 部门管理
   - 岗位管理
   - 密码安全策略
     - 密码错误次数限制（默认5次）
     - 账户锁定时间（默认10分钟）
     - 管理员手动解锁功能

2. 系统管理
   - 菜单管理
   - 字典管理
   - 参数设置
   - 通知公告
   - 日志管理
     - 操作日志
     - 登录日志
     - 登录失败记录
     - 账户锁定记录

3. 系统监控
   - 在线用户
   - 服务监控
   - 缓存监控
   - 表单构建

4. 系统工具
   - 代码生成
   - 系统接口文档

5. 质量保证管理
   - 文件上传功能
     - 支持多文件上传
     - 自动解析文件名提取ASN号
     - 文件信息保存到数据库
     - 支持文件内容解析（待实现）

## 开发环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 6.0+

## 快速开始
1. 克隆项目到本地
2. 创建数据库并导入sql目录下的数据库脚本
3. 修改数据库连接配置（application-druid.yml）
4. 运行项目
   - Windows: 运行 `ry.bat`
   - Linux: 运行 `ry.sh`

## 项目结构
```
QualityAssurance_backend
├── ruoyi-admin        // 后台管理模块
├── ruoyi-framework    // 核心框架模块
├── ruoyi-system       // 系统管理模块
├── ruoyi-quartz       // 定时任务模块
├── ruoyi-generator    // 代码生成模块
├── ruoyi-common       // 公共工具模块
├── sql                // 数据库脚本
└── pom.xml           // 项目依赖管理
```

## 安全特性
1. 密码安全
   - 密码长度限制：5-20个字符
   - 密码错误限制：最多5次
   - 账户锁定时间：10分钟
   - 支持管理员手动解锁

2. 登录安全
   - 验证码支持
   - IP黑名单
   - 登录日志记录
   - 异常登录提醒

3. 权限控制
   - 基于角色的访问控制
   - 细粒度的权限管理
   - 数据权限控制

## 接口文档
- 接口文档地址：http://localhost:8080/swagger-ui/index.html
- 接口测试地址：http://localhost:8080/swagger-ui/index.html

## 注意事项
1. 首次运行需要执行sql目录下的数据库脚本
2. 默认管理员账号：admin
3. 默认密码：admin123
4. 建议在开发环境中使用，生产环境部署前请修改默认密码
5. 密码错误5次后账户将被锁定10分钟
6. 管理员可以通过登录日志管理页面手动解锁被锁定的账户

## 更新日志
### v1.0.0 (2024-04-02)
- 初始化项目
- 基于RuoYi框架开发
- 实现基础功能模块
- 添加密码安全策略
- 实现账户锁定功能
- 添加管理员解锁功能

## 贡献指南
1. Fork 本仓库
2. 创建新的功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证
本项目采用 MIT 许可证 