package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeCategoryMapper;
import com.ruoyi.system.domain.AiDataExchangeCategory;
import com.ruoyi.system.service.IAiDataExchangeCategoryService;

/**
 * AI数据交换的产品类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeCategoryServiceImpl implements IAiDataExchangeCategoryService 
{
    @Autowired
    private AiDataExchangeCategoryMapper aiDataExchangeCategoryMapper;

    /**
     * 查询AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategoryId AI数据交换的产品类别主键
     * @return AI数据交换的产品类别
     */
    @Override
    public AiDataExchangeCategory selectAiDataExchangeCategoryByAiDataExchangeCategoryId(Long aiDataExchangeCategoryId)
    {
        return aiDataExchangeCategoryMapper.selectAiDataExchangeCategoryByAiDataExchangeCategoryId(aiDataExchangeCategoryId);
    }

    /**
     * 查询AI数据交换的产品类别列表
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return AI数据交换的产品类别
     */
    @Override
    public List<AiDataExchangeCategory> selectAiDataExchangeCategoryList(AiDataExchangeCategory aiDataExchangeCategory)
    {
        return aiDataExchangeCategoryMapper.selectAiDataExchangeCategoryList(aiDataExchangeCategory);
    }

    /**
     * 新增AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeCategory(AiDataExchangeCategory aiDataExchangeCategory)
    {
        return aiDataExchangeCategoryMapper.insertAiDataExchangeCategory(aiDataExchangeCategory);
    }

    /**
     * 修改AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeCategory(AiDataExchangeCategory aiDataExchangeCategory)
    {
        return aiDataExchangeCategoryMapper.updateAiDataExchangeCategory(aiDataExchangeCategory);
    }

    /**
     * 批量删除AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategoryIds 需要删除的AI数据交换的产品类别主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeCategoryByAiDataExchangeCategoryIds(Long[] aiDataExchangeCategoryIds)
    {
        return aiDataExchangeCategoryMapper.deleteAiDataExchangeCategoryByAiDataExchangeCategoryIds(aiDataExchangeCategoryIds);
    }

    /**
     * 删除AI数据交换的产品类别信息
     * 
     * @param aiDataExchangeCategoryId AI数据交换的产品类别主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeCategoryByAiDataExchangeCategoryId(Long aiDataExchangeCategoryId)
    {
        return aiDataExchangeCategoryMapper.deleteAiDataExchangeCategoryByAiDataExchangeCategoryId(aiDataExchangeCategoryId);
    }
}
