package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeCategory;

/**
 * AI数据交换的产品类别Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IAiDataExchangeCategoryService 
{
    /**
     * 查询AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategoryId AI数据交换的产品类别主键
     * @return AI数据交换的产品类别
     */
    public AiDataExchangeCategory selectAiDataExchangeCategoryByAiDataExchangeCategoryId(Long aiDataExchangeCategoryId);

    /**
     * 查询AI数据交换的产品类别列表
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return AI数据交换的产品类别集合
     */
    public List<AiDataExchangeCategory> selectAiDataExchangeCategoryList(AiDataExchangeCategory aiDataExchangeCategory);

    /**
     * 新增AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return 结果
     */
    public int insertAiDataExchangeCategory(AiDataExchangeCategory aiDataExchangeCategory);

    /**
     * 修改AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategory AI数据交换的产品类别
     * @return 结果
     */
    public int updateAiDataExchangeCategory(AiDataExchangeCategory aiDataExchangeCategory);

    /**
     * 批量删除AI数据交换的产品类别
     * 
     * @param aiDataExchangeCategoryIds 需要删除的AI数据交换的产品类别主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeCategoryByAiDataExchangeCategoryIds(Long[] aiDataExchangeCategoryIds);

    /**
     * 删除AI数据交换的产品类别信息
     * 
     * @param aiDataExchangeCategoryId AI数据交换的产品类别主键
     * @return 结果
     */
    public int deleteAiDataExchangeCategoryByAiDataExchangeCategoryId(Long aiDataExchangeCategoryId);
}
