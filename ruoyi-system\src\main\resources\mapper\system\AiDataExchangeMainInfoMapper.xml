<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeMainInfoMapper">

    <resultMap type="AiDataExchangeMainInfo" id="AiDataExchangeMainInfoResult">
        <result property="aiDataExchangeMainInfoId"    column="ai_data_exchange_main_info_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="status"    column="status"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="inspectedById"    column="inspected_by_id"    />
        <result property="inspectedBy"    column="inspected_by"    />
        <result property="checkedBy"    column="checked_by"    />
        <result property="approvedBy"    column="approved_by"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="factory"    column="factory"    />
        <result property="idsId"    column="ids_id"    />
        <result property="ids"    column="ids"    />
        <result property="functionId"    column="function_id"    />
        <result property="function"    column="function"    />
        <result property="deptId"    column="dept_id"    />
        <result property="section"    column="section"    />
        <result property="aiDataExchangeCategoryId"    column="ai_data_exchange_category_id"    />
        <result property="aiDataExchangeProductId"    column="ai_data_exchange_product_id"    />
        <result property="makerPartNoId"    column="maker_part_no_id"    />
        <result property="asnNo"    column="asn_no"    />
        <result property="partNo"    column="part_no"    />
        <result property="customerName"    column="customer_name"    />
        <result property="categoryName"    column="category_name"    />
        <result property="productName"    column="product_name"    />
        <result property="makerPartNo"    column="maker_part_no"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAiDataExchangeMainInfoVo">
        select a.ai_data_exchange_main_info_id, a.file_name, a.file_path, a.status, a.reject_reason,
               a.inspected_by_id, a.inspected_by, a.checked_by, a.approved_by,
               a.factory_id, a.factory, a.ids_id, a.ids, a.function_id, a.function, a.dept_id, a.section,
               a.ai_data_exchange_category_id, a.ai_data_exchange_product_id, a.maker_part_no_id,
               a.asn_no, a.part_no, a.customer_name, a.create_time, a.update_time
        from ai_data_exchange_main_info a
    </sql>

    <sql id="selectAiDataExchangeMainInfoWithDetailsVo">
        select a.ai_data_exchange_main_info_id, a.file_name, a.file_path, a.status, a.reject_reason,
               a.inspected_by_id, a.inspected_by, a.checked_by, a.approved_by,
               a.factory_id, a.factory, a.ids_id, a.ids, a.function_id, a.function, a.dept_id, a.section,
               a.ai_data_exchange_category_id, a.ai_data_exchange_product_id, a.maker_part_no_id,
               a.asn_no, a.part_no, a.customer_name, a.create_time, a.update_time,
               c.category_name, p.product_name, m.maker_part_no
        from ai_data_exchange_main_info a
        left join ai_data_exchange_category c on a.ai_data_exchange_category_id = c.ai_data_exchange_category_id
        left join ai_data_exchange_product p on a.ai_data_exchange_product_id = p.ai_data_exchange_product_id
        left join ai_data_exchange_maker_part_no m on a.maker_part_no_id = m.ai_data_exchange_maker_part_no_id
    </sql>

    <select id="selectAiDataExchangeMainInfoList" parameterType="AiDataExchangeMainInfo" resultMap="AiDataExchangeMainInfoResult">
        <include refid="selectAiDataExchangeMainInfoWithDetailsVo"/>
        <where>
            <if test="factoryId != null "> and a.factory_id = #{factoryId}</if>
            <if test="idsId != null "> and a.ids_id = #{idsId}</if>
            <if test="functionId != null "> and a.function_id = #{functionId}</if>
            <if test="deptId != null "> and a.dept_id = #{deptId}</if>
            <if test="aiDataExchangeCategoryId != null "> and a.ai_data_exchange_category_id = #{aiDataExchangeCategoryId}</if>
            <if test="aiDataExchangeProductId != null "> and a.ai_data_exchange_product_id = #{aiDataExchangeProductId}</if>
            <if test="makerPartNoId != null "> and a.maker_part_no_id = #{makerPartNoId}</if>
            <if test="asnNo != null  and asnNo != ''"> and a.asn_no like concat('%', #{asnNo}, '%')</if>
            <if test="partNo != null  and partNo != ''"> and a.part_no like concat('%', #{partNo}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and a.customer_name like concat('%', #{customerName}, '%')</if>
            <if test="categoryName != null and categoryName != ''"> and c.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="productName != null and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="makerPartNo != null and makerPartNo != ''"> and m.maker_part_no like concat('%', #{makerPartNo}, '%')</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId" parameterType="Long" resultMap="AiDataExchangeMainInfoResult">
        <include refid="selectAiDataExchangeMainInfoWithDetailsVo"/>
        where a.ai_data_exchange_main_info_id = #{aiDataExchangeMainInfoId}
    </select>

    <insert id="insertAiDataExchangeMainInfo" parameterType="AiDataExchangeMainInfo" useGeneratedKeys="true" keyProperty="aiDataExchangeMainInfoId">
        insert into ai_data_exchange_main_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="status != null">status,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="inspectedById != null">inspected_by_id,</if>
            <if test="inspectedBy != null and inspectedBy != ''">inspected_by,</if>
            <if test="checkedBy != null and checkedBy != ''">checked_by,</if>
            <if test="approvedBy != null and approvedBy != ''">approved_by,</if>
            <if test="factoryId != null">factory_id,</if>
            <if test="factory != null and factory != ''">factory,</if>
            <if test="idsId != null">ids_id,</if>
            <if test="ids != null and ids != ''">ids,</if>
            <if test="functionId != null">function_id,</if>
            <if test="function != null and function != ''">function,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="section != null and section != ''">section,</if>
            <if test="aiDataExchangeCategoryId != null">ai_data_exchange_category_id,</if>
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id,</if>
            <if test="makerPartNoId != null">maker_part_no_id,</if>
            <if test="asnNo != null">asn_no,</if>
            <if test="partNo != null">part_no,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="status != null">#{status},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="inspectedById != null">#{inspectedById},</if>
            <if test="inspectedBy != null and inspectedBy != ''">#{inspectedBy},</if>
            <if test="checkedBy != null and checkedBy != ''">#{checkedBy},</if>
            <if test="approvedBy != null and approvedBy != ''">#{approvedBy},</if>
            <if test="factoryId != null">#{factoryId},</if>
            <if test="factory != null and factory != ''">#{factory},</if>
            <if test="idsId != null">#{idsId},</if>
            <if test="ids != null and ids != ''">#{ids},</if>
            <if test="functionId != null">#{functionId},</if>
            <if test="function != null and function != ''">#{function},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="section != null and section != ''">#{section},</if>
            <if test="aiDataExchangeCategoryId != null">#{aiDataExchangeCategoryId},</if>
            <if test="aiDataExchangeProductId != null">#{aiDataExchangeProductId},</if>
            <if test="makerPartNoId != null">#{makerPartNoId},</if>
            <if test="asnNo != null">#{asnNo},</if>
            <if test="partNo != null">#{partNo},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeMainInfo" parameterType="AiDataExchangeMainInfo">
        update ai_data_exchange_main_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="inspectedById != null">inspected_by_id = #{inspectedById},</if>
            <if test="inspectedBy != null and inspectedBy != ''">inspected_by = #{inspectedBy},</if>
            <if test="checkedBy != null and checkedBy != ''">checked_by = #{checkedBy},</if>
            <if test="approvedBy != null and approvedBy != ''">approved_by = #{approvedBy},</if>
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="factory != null and factory != ''">factory = #{factory},</if>
            <if test="idsId != null">ids_id = #{idsId},</if>
            <if test="ids != null and ids != ''">ids = #{ids},</if>
            <if test="functionId != null">function_id = #{functionId},</if>
            <if test="function != null and function != ''">function = #{function},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="section != null and section != ''">section = #{section},</if>
            <if test="aiDataExchangeCategoryId != null">ai_data_exchange_category_id = #{aiDataExchangeCategoryId},</if>
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id = #{aiDataExchangeProductId},</if>
            <if test="makerPartNoId != null">maker_part_no_id = #{makerPartNoId},</if>
            <if test="asnNo != null">asn_no = #{asnNo},</if>
            <if test="partNo != null">part_no = #{partNo},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ai_data_exchange_main_info_id = #{aiDataExchangeMainInfoId}
    </update>

    <delete id="deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoId" parameterType="Long">
        delete from ai_data_exchange_main_info where ai_data_exchange_main_info_id = #{aiDataExchangeMainInfoId}
    </delete>

    <delete id="deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds" parameterType="String">
        delete from ai_data_exchange_main_info where ai_data_exchange_main_info_id in
        <foreach item="aiDataExchangeMainInfoId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeMainInfoId}
        </foreach>
    </delete>
</mapper>