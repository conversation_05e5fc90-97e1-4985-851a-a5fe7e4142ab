package com.ruoyi.system.service.impl;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.system.domain.AiDataExchangeCategory;
import com.ruoyi.system.domain.AiDataExchangeMainInfo;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;
import com.ruoyi.system.domain.AiDataExchangeProduct;

import com.ruoyi.system.domain.mitac.MiTACQualityReportRequest;
import com.ruoyi.system.domain.mitac.MiTACQualityReportResponse;

import com.ruoyi.system.mapper.AiDataExchangeCategoryMapper;
import com.ruoyi.system.mapper.AiDataExchangeCategoryProductMapper;
import com.ruoyi.system.mapper.AiDataExchangeMainInfoMapper;
import com.ruoyi.system.mapper.AiDataExchangeMakerPartNoMapper;
import com.ruoyi.system.mapper.AiDataExchangeProductMapper;
import com.ruoyi.system.mapper.AiDataExchangeProductMakerPartNoMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.IAiDataExchangeMainInfoService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.utils.ExcelParser;
import com.ruoyi.system.email.service.Email;

/**
 * AI数据交换Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeMainInfoServiceImpl implements IAiDataExchangeMainInfoService
{
    private static final Logger log = LoggerFactory.getLogger(AiDataExchangeMainInfoServiceImpl.class);

    /** 文件保存根路径 */
    private static final String FILE_SAVE_PATH = "D:/AiDataExchangeFile/";

    /** 允许上传的文件类型 */
    private static final String[] ALLOWED_EXTENSIONS = {"xls", "xlsx"};

    @Autowired
    private AiDataExchangeMainInfoMapper aiDataExchangeMainInfoMapper;

    @Autowired
    private AiDataExchangeCategoryMapper aiDataExchangeCategoryMapper;

    @Autowired
    private AiDataExchangeCategoryProductMapper aiDataExchangeCategoryProductMapper;

    @Autowired
    private AiDataExchangeProductMapper aiDataExchangeProductMapper;

    @Autowired
    private AiDataExchangeMakerPartNoMapper aiDataExchangeMakerPartNoMapper;

    @Autowired
    private AiDataExchangeProductMakerPartNoMapper aiDataExchangeProductMakerPartNoMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private MiTACServiceImpl miTACService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private Email emailService;

    /**
     * 查询AI数据交换
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return AI数据交换
     */
    @Override
    public AiDataExchangeMainInfo selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId)
    {
        return aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
    }

    /**
     * 查询AI数据交换列表
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return AI数据交换
     */
    @Override
    public List<AiDataExchangeMainInfo> selectAiDataExchangeMainInfoList(AiDataExchangeMainInfo aiDataExchangeMainInfo)
    {
        return aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoList(aiDataExchangeMainInfo);
    }

    /**
     * 新增AI数据交换
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo)
    {
        aiDataExchangeMainInfo.setCreateTime(DateUtils.getNowDate());
        return aiDataExchangeMainInfoMapper.insertAiDataExchangeMainInfo(aiDataExchangeMainInfo);
    }

    /**
     * 修改AI数据交换
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo)
    {
        aiDataExchangeMainInfo.setUpdateTime(DateUtils.getNowDate());
        return aiDataExchangeMainInfoMapper.updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);
    }

    /**
     * 批量删除AI数据交换
     *
     * @param aiDataExchangeMainInfoIds 需要删除的AI数据交换主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds(Long[] aiDataExchangeMainInfoIds)
    {
        return aiDataExchangeMainInfoMapper.deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds(aiDataExchangeMainInfoIds);
    }

    /**
     * 删除AI数据交换信息
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId)
    {
        return aiDataExchangeMainInfoMapper.deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
    }

    /**
     * 处理Excel文件上传和解析的通用方法
     *
     * @param file 上传的文件
     * @param userId 用户ID
     * @param deptId 课别ID（可为null，用于重新提交时不需要更新部门ID）
     * @param factoryId 厂区ID（可为null，用于重新提交时不需要更新厂区ID）
     * @param idsId 产品处IDS ID（可为null，用于重新提交时不需要更新产品处IDS ID）
     * @param functionId 机能ID（可为null，用于重新提交时不需要更新机能ID）
     * @param aiDataExchangeMainInfo 现有记录（可为null，用于新上传时创建新记录）
     * @param currentId 当前记录ID（可为null，用于检查唯一性时排除当前记录）
     * @return 处理结果，包含解析后的记录和错误信息
     */
    private Map<String, Object> processExcelFile(MultipartFile file, Long userId, Long deptId, Long factoryId, Long idsId, Long functionId, AiDataExchangeMainInfo aiDataExchangeMainInfo, Long currentId) {
        Map<String, Object> result = new HashMap<>();
        File dest = null;

        try {
            // 校验文件是否为空
            if (file.isEmpty()) {
                result.put("error", "上传文件不能为空");
                return result;
            }

            // 校验文件类型
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            if (!isAllowedExtension(extension)) {
                result.put("error", "文件格式不正确，请上传Excel文件");
                return result;
            }

            // 按照年月创建目录
            SimpleDateFormat dirFormat = new SimpleDateFormat("yyyy/MM");
            String datePath = dirFormat.format(new Date());
            String dirPath = FILE_SAVE_PATH + datePath;

            // 创建目录
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成新的文件名
            String originalFilename = file.getOriginalFilename();
            String newFilename = UUID.randomUUID().toString() + "." + extension;
            String filePath = dirPath + "/" + newFilename;

            // 保存文件到本地
            dest = new File(filePath);
            file.transferTo(dest);

            // 解析Excel文件（从已保存的本地文件解析，而不是从MultipartFile中获取流）
            MiTACQualityReportRequest reportRequest;
            try {
                reportRequest = ExcelParser.parseExcelFromFile(dest);
            } catch (Exception e) {
                log.error("解析Excel文件失败：", e);
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                result.put("error", "解析Excel文件失败：" + e.getMessage());
                return result;
            }

            // 创建或更新记录
            if (aiDataExchangeMainInfo == null) {
                aiDataExchangeMainInfo = new AiDataExchangeMainInfo();
                aiDataExchangeMainInfo.setStatus("0"); // 0表示待复核状态
                aiDataExchangeMainInfo.setInspectedById(userId);
                if (deptId != null) {
                    aiDataExchangeMainInfo.setDeptId(deptId);
                    // 查询课别名称
                    SysDept dept = sysDeptService.selectDeptById(deptId);
                    if (dept != null) {
                        aiDataExchangeMainInfo.setSection(dept.getDeptName());
                    }
                }
                if (factoryId != null) {
                    aiDataExchangeMainInfo.setFactoryId(factoryId);
                    // 查询厂区名称
                    SysDept factory = sysDeptService.selectDeptById(factoryId);
                    if (factory != null) {
                        aiDataExchangeMainInfo.setFactory(factory.getDeptName());
                    }
                }
                if (idsId != null) {
                    aiDataExchangeMainInfo.setIdsId(idsId);
                    // 查询产品处IDS名称
                    SysDept ids = sysDeptService.selectDeptById(idsId);
                    if (ids != null) {
                        aiDataExchangeMainInfo.setIds(ids.getDeptName());
                    }
                }
                if (functionId != null) {
                    aiDataExchangeMainInfo.setFunctionId(functionId);
                    // 查询机能名称
                    SysDept function = sysDeptService.selectDeptById(functionId);
                    if (function != null) {
                        aiDataExchangeMainInfo.setFunction(function.getDeptName());
                    }
                }
            } else {
                aiDataExchangeMainInfo.setStatus("0"); // 重置为待复核状态
                aiDataExchangeMainInfo.setRejectReason(" "); // 清空驳回原因
            }

            aiDataExchangeMainInfo.setFileName(originalFilename);
            aiDataExchangeMainInfo.setFilePath(filePath);

            // 设置ASN号和客户料号
            String asnNo = reportRequest.getAsnNo();
            String partNo = reportRequest.getPartNo();

            // 检查客户料号和送货单号是否已存在
            if (StringUtils.isNotEmpty(partNo) && StringUtils.isNotEmpty(asnNo)) {
                boolean exists;
                if (currentId != null) {
                    exists = checkPartNoAndAsnNoExistsExcludeCurrent(partNo, asnNo, currentId);
                } else {
                    exists = checkPartNoAndAsnNoExists(partNo, asnNo);
                }

                if (exists) {
                    if (dest != null && dest.exists()) {
                        dest.delete();
                    }
                    result.put("error", "该客户料号(" + partNo + ")和送货单号(" + asnNo + ")的组合已存在，请检查");
                    return result;
                }
            } else {
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                if (StringUtils.isEmpty(partNo)) {
                    result.put("error", "客户料号不能为空");
                    return result;
                }
                if (StringUtils.isEmpty(asnNo)) {
                    result.put("error", "送货单号不能为空");
                    return result;
                }
            }

            aiDataExchangeMainInfo.setAsnNo(asnNo);
            aiDataExchangeMainInfo.setPartNo(partNo);

            // 处理查询参数：转大写并去除空白字符
            String categoryName = StringUtils.isNotEmpty(reportRequest.getCategory()) ?
                    reportRequest.getCategory().toUpperCase().replaceAll("\\s+", "") : null;
            String productName = StringUtils.isNotEmpty(reportRequest.getDescription()) ?
                    reportRequest.getDescription().toUpperCase().replaceAll("\\s+", "") : null;
            String makerPartNoStr = StringUtils.isNotEmpty(reportRequest.getMakerPartNo()) ?
                    reportRequest.getMakerPartNo().toUpperCase().replaceAll("\\s+", "") : null;

            // 根据MiTACQualityReportRequest的category查询ai_data_exchange_category_id
            Long categoryId = getCategoryIdByName(categoryName);
            if (categoryId == null) {
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                result.put("error", "未找到对应的产品类别，请检查类别名称: " + reportRequest.getCategory());
                return result;
            }

            aiDataExchangeMainInfo.setAiDataExchangeCategoryId(categoryId);
            aiDataExchangeMainInfo.setCategoryName(categoryName);

            // 根据ai_data_exchange_category_id和description查询ai_data_exchange_product_id
            Long productId = getProductIdByCategoryIdAndName(categoryId, productName);
            if (productId == null) {
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                result.put("error", "未找到对应的产品信息，请检查产品名称: " + reportRequest.getDescription());
                return result;
            }

            aiDataExchangeMainInfo.setAiDataExchangeProductId(productId);
            aiDataExchangeMainInfo.setProductName(productName);

            // 根据ai_data_exchange_product_id和makerPartNo查询maker_part_no_id
            Long makerPartNoId = getMakerPartNoIdByProductIdAndMakerPartNo(productId, makerPartNoStr);
            if (makerPartNoId == null) {
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                result.put("error", "未找到对应的Maker料号信息，请检查Maker料号: " + reportRequest.getMakerPartNo());
                return result;
            }

            aiDataExchangeMainInfo.setMakerPartNoId(makerPartNoId);
            aiDataExchangeMainInfo.setMakerPartNo(makerPartNoStr);

            // 验证签核人员
            String validationResult = validateApprovers(reportRequest.getApprovedBy(), reportRequest.getCheckedBy());
            if (validationResult != null) {
                if (dest != null && dest.exists()) {
                    dest.delete();
                }
                result.put("error", validationResult);
                return result;
            }

            // 设置检验员、复核人和核定人名称
//            // 获取当前用户的昵称
//            SysUser currentUser = sysUserMapper.selectUserById(userId);
//            if (currentUser != null && StringUtils.isNotEmpty(currentUser.getNickName())) {
//                aiDataExchangeMainInfo.setInspectedBy(currentUser.getNickName());
//            }
            //新版設置檢驗員
            aiDataExchangeMainInfo.setInspectedBy(reportRequest.getInspectedBy());

            // 设置复核人和核定人名称
            aiDataExchangeMainInfo.setCheckedBy(reportRequest.getCheckedBy());
            aiDataExchangeMainInfo.setApprovedBy(reportRequest.getApprovedBy());

            //设置客户名称
            aiDataExchangeMainInfo.setCustomerName(reportRequest.getCustomer());

            // 返回处理结果
            result.put("success", true);
            result.put("aiDataExchangeMainInfo", aiDataExchangeMainInfo);
            result.put("reportRequest", reportRequest);

            return result;
        } catch (Exception e) {
            log.error("处理Excel文件异常：", e);
            // 删除临时文件
            if (dest != null && dest.exists()) {
                dest.delete();
            }
            result.put("error", "处理Excel文件异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 上传Excel文件
     *
     * @param file 上传的文件
     * @param deptId 课别ID
     * @param factoryId 厂区ID
     * @param idsId 产品处IDS ID
     * @param functionId 机能ID
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file, Long deptId, Long factoryId, Long idsId, Long functionId, Long userId)
    {
        try
        {
            // 调用通用方法处理文件上传和解析
            Map<String, Object> processResult = processExcelFile(file, userId, deptId, factoryId, idsId, functionId, null, null);

            // 检查是否有错误
            if (processResult.containsKey("error")) {
                return AjaxResult.error(processResult.get("error").toString());
            }

            // 获取处理结果
            AiDataExchangeMainInfo aiDataExchangeMainInfo = (AiDataExchangeMainInfo) processResult.get("aiDataExchangeMainInfo");

            // 保存到数据库
            insertAiDataExchangeMainInfo(aiDataExchangeMainInfo);

            // 发送提醒邮件给复核人
            String checkerEmail = getUserEmail(aiDataExchangeMainInfo.getCheckedBy(),"checkedBy");
            if (StringUtils.isNotEmpty(checkerEmail)) {
                sendNotificationEmail(checkerEmail, aiDataExchangeMainInfo, "复核", aiDataExchangeMainInfo.getCheckedBy());
            } else {
                log.warn("未找到复核人邮箱，复核人昵称：{}", aiDataExchangeMainInfo.getCheckedBy());
            }

            // 返回成功信息和aiDataExchangeMainInfo对象
            return AjaxResult.success("上传成功").put("data", aiDataExchangeMainInfo);
        }
        catch (Exception e)
        {
            log.error("文件上传失败：", e);
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 根据类别名称查询类别ID
     *
     * @param categoryName 类别名称（已转大写并去除空白字符）
     * @return 类别ID
     */
    private Long getCategoryIdByName(String categoryName) {
        if (StringUtils.isEmpty(categoryName)) {
            return null;
        }

        AiDataExchangeCategory category = new AiDataExchangeCategory();
        category.setCategoryName(categoryName);
        List<AiDataExchangeCategory> categories = aiDataExchangeCategoryMapper.selectAiDataExchangeCategoryList(category);

        if (categories != null && !categories.isEmpty()) {
            return categories.get(0).getAiDataExchangeCategoryId();
        }

        return null;
    }

    /**
     * 根据类别ID和产品名称查询产品ID
     *
     * @param categoryId 类别ID
     * @param productName 产品名称（已转大写并去除空白字符）
     * @return 产品ID
     */
    private Long getProductIdByCategoryIdAndName(Long categoryId, String productName) {
        if (categoryId == null || StringUtils.isEmpty(productName)) {
            return null;
        }

        // 先根据产品名称查询产品
        AiDataExchangeProduct product = new AiDataExchangeProduct();
        product.setProductName(productName);
        List<AiDataExchangeProduct> products = aiDataExchangeProductMapper.selectAiDataExchangeProductList(product);

        if (products != null && !products.isEmpty()) {
            for (AiDataExchangeProduct p : products) {
                // 直接使用selectAiDataExchangeCategoryProductByIds方法检查产品是否属于指定类别
                if (aiDataExchangeCategoryProductMapper.selectAiDataExchangeCategoryProductByIds(
                        categoryId, p.getAiDataExchangeProductId()) != null) {
                    return p.getAiDataExchangeProductId();
                }
            }
        }

        return null;
    }

    /**
     * 根据产品ID和Maker料号查询Maker料号ID
     *
     * @param productId 产品ID
     * @param makerPartNo Maker料号（已转大写并去除空白字符）
     * @return Maker料号ID
     */
    private Long getMakerPartNoIdByProductIdAndMakerPartNo(Long productId, String makerPartNo) {
        if (productId == null || StringUtils.isEmpty(makerPartNo)) {
            return null;
        }

        // 先根据Maker料号查询
        AiDataExchangeMakerPartNo makerPartNoObj = new AiDataExchangeMakerPartNo();
        makerPartNoObj.setMakerPartNo(makerPartNo);
        List<AiDataExchangeMakerPartNo> makerPartNos = aiDataExchangeMakerPartNoMapper.selectAiDataExchangeMakerPartNoList(makerPartNoObj);

        if (makerPartNos != null && !makerPartNos.isEmpty()) {
            for (AiDataExchangeMakerPartNo mpn : makerPartNos) {
                // 直接使用selectAiDataExchangeProductMakerPartNoByIds方法检查Maker料号是否属于指定产品
                if (aiDataExchangeProductMakerPartNoMapper.selectAiDataExchangeProductMakerPartNoByIds(
                        productId, mpn.getAiDataExchangeMakerPartNoId()) != null) {
                    return mpn.getAiDataExchangeMakerPartNoId();
                }
            }
        }

        return null;
    }

    /**
     * 判断文件扩展名是否允许
     *
     * @param extension 文件扩展名
     * @return 是否允许
     */
    private boolean isAllowedExtension(String extension)
    {
        if (extension != null)
        {
            for (String allowedExtension : ALLOWED_EXTENSIONS)
            {
                if (allowedExtension.equalsIgnoreCase(extension))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检查客户料号和送货单号组合是否已存在
     *
     * @param partNo 客户料号
     * @param asnNo 送货单号
     * @return 是否存在
     */
    private boolean checkPartNoAndAsnNoExists(String partNo, String asnNo) {
        AiDataExchangeMainInfo query = new AiDataExchangeMainInfo();
        query.setPartNo(partNo);
        query.setAsnNo(asnNo);
        List<AiDataExchangeMainInfo> list = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoList(query);
        return list != null && !list.isEmpty();
    }

    /**
     * 验证签核人员
     *
     * @param approvedBy 核定人
     * @param checkedBy 复核人
     * @return 验证结果，如果验证通过则返回null，否则返回错误信息
     */
    private String validateApprovers(String approvedBy, String checkedBy) {
        if (StringUtils.isEmpty(approvedBy)) {
            return "核定人不能为空";
        }

        if (StringUtils.isEmpty(checkedBy)) {
            return "复核人不能为空";
        }

        // 直接验证sys_user表中是否存在指定的用户昵称
        // 检查核定人
        List<SysUser> approvedUsers = sysUserMapper.selectUserByNickName(approvedBy);
        if (approvedUsers == null || approvedUsers.isEmpty()) {
            return "核定人(" + approvedBy + ")不存在";
        }

        // 检查复核人
        List<SysUser> checkedUsers = sysUserMapper.selectUserByNickName(checkedBy);
        if (checkedUsers == null || checkedUsers.isEmpty()) {
            return "复核人(" + checkedBy + ")不存在";
        }

        return null;
    }

    /**
     * 下载文件
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param response HTTP响应对象
     */
    @Override
    public void downloadFile(Long aiDataExchangeMainInfoId, HttpServletResponse response) {
        try {
            // 根据ID查询文件信息
            AiDataExchangeMainInfo fileInfo = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
            if (fileInfo == null || StringUtils.isEmpty(fileInfo.getFilePath())) {
                throw new RuntimeException("文件不存在");
            }

            // 获取文件路径和原始文件名
            String filePath = fileInfo.getFilePath();
            String fileName = fileInfo.getFileName();

            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                throw new RuntimeException("文件不存在或已被删除");
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            FileUtils.setAttachmentResponseHeader(response, fileName);

            // 写入响应流
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (Exception e) {
            log.error("文件下载失败：", e);
            throw new RuntimeException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 核定并发送质检报告到MiTAC
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 处理结果
     */
    @Override
    public AjaxResult approveAndSendReport(Long aiDataExchangeMainInfoId) {
        try {
            // 查询当前记录
            AiDataExchangeMainInfo aiDataExchangeMainInfo = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
            if (aiDataExchangeMainInfo == null) {
                return AjaxResult.error("未找到对应的记录");
            }

            // 检查当前状态是否为待核定(1)
            if (!"1".equals(aiDataExchangeMainInfo.getStatus())) {
                return AjaxResult.error("当前记录状态不是待核定状态，无法进行核定操作");
            }

            // 获取当前登录用户
            String currentUserNickName = SecurityUtils.getLoginUser().getUser().getNickName();

            // 验证当前用户是否为记录中的核定人
            if (!currentUserNickName.equals(aiDataExchangeMainInfo.getApprovedBy())) {
                return AjaxResult.error("无法修改不属于自己的记录，当前记录的核定人为：" + aiDataExchangeMainInfo.getApprovedBy());
            }

            // 获取文件路径
            String filePath = aiDataExchangeMainInfo.getFilePath();
            if (StringUtils.isEmpty(filePath)) {
                return AjaxResult.error("文件路径为空");
            }

            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                return AjaxResult.error("文件不存在或已被删除");
            }

            // 解析Excel文件
            MiTACQualityReportRequest reportRequest;
            try {
                reportRequest = ExcelParser.parseExcelFromFile(file);
            } catch (Exception e) {
                log.error("解析Excel文件失败：", e);
                return AjaxResult.error("解析Excel文件失败：" + e.getMessage());
            }

            // 检查检验结果是否为OK
            if(!"OK".equals(reportRequest.getResult())){
                return AjaxResult.error("判定結果为NG！无法抛转。");
            }

            // 发送质检报告到MiTAC
            try {
                MiTACQualityReportResponse response = miTACService.sendQualityReport(reportRequest);
                log.info("发送质检报告响应: code={}, success={}, msg={}",
                        response.getCode(), response.getSuccess(), response.getMsg());

                // 检查发送结果
                if (response.getSuccess() != null && response.getSuccess()) {
                    // 发送成功，修改状态为已完成(2)
                    aiDataExchangeMainInfo.setStatus("2");
                    updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);
                    return AjaxResult.success("核定成功，质检报告已发送到MiTAC");
                } else {
                    // 发送失败，但有明确的错误信息
                    String errorMsg = response.getMsg();

                    // 检查是否是重复提交的错误
                    if (response.getCode() != null && response.getCode() == 400 &&
                            errorMsg != null && errorMsg.contains("已经推送过质检报告")) {
                        // 对于重复提交的情况，也视为成功，更新状态为已完成
                        log.warn("质检报告重复提交，视为成功: {}", errorMsg);
                        aiDataExchangeMainInfo.setStatus("2");
                        updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);
                        return AjaxResult.success("核定成功，但MiTAC提示: " + errorMsg);
                    }

                    // 其他错误情况
                    return AjaxResult.error("发送质检报告失败：" + errorMsg);
                }
            } catch (Exception e) {
                log.error("发送质检报告异常：", e);
                return AjaxResult.error("发送质检报告异常：" + e.getMessage());
            }
        } catch (Exception e) {
            log.error("核定操作异常：", e);
            return AjaxResult.error("核定操作异常：" + e.getMessage());
        }
    }

    /**
     * 驳回记录
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param rejectReason 驳回原因
     * @param userId 驳回人ID
     * @return 处理结果
     */
    @Override
    public AjaxResult rejectRecord(Long aiDataExchangeMainInfoId, String rejectReason, Long userId) {
        try {
            // 查询当前记录
            AiDataExchangeMainInfo aiDataExchangeMainInfo = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
            if (aiDataExchangeMainInfo == null) {
                return AjaxResult.error("未找到对应的记录");
            }

            // 检查当前状态是否为待复核(0)或待核定(1)
            if (!"0".equals(aiDataExchangeMainInfo.getStatus()) && !"1".equals(aiDataExchangeMainInfo.getStatus())) {
                return AjaxResult.error("当前记录状态不是待复核或待核定状态，无法进行驳回操作");
            }

            // 获取驳回人信息
            SysUser user = sysUserMapper.selectUserById(userId);
            if (user == null) {
                return AjaxResult.error("未找到驳回人信息");
            }

            // 在驳回原因前加上驳回人昵称
            String nickName = user.getNickName();
            String formattedRejectReason = "[" + nickName + "] " + rejectReason;

            // 修改状态为已驳回(3)并设置驳回原因
            aiDataExchangeMainInfo.setStatus("3");
            aiDataExchangeMainInfo.setRejectReason(formattedRejectReason);

            // 更新记录
            updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);

            return AjaxResult.success("驳回成功");
        } catch (Exception e) {
            log.error("驳回操作异常：", e);
            return AjaxResult.error("驳回操作异常：" + e.getMessage());
        }
    }

    /**
     * 重新提交文件
     *
     * @param file 上传的文件
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    public AjaxResult resubmitFile(MultipartFile file, Long aiDataExchangeMainInfoId, Long userId) {
        try {
            // 查询当前记录
            AiDataExchangeMainInfo aiDataExchangeMainInfo = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
            if (aiDataExchangeMainInfo == null) {
                return AjaxResult.error("未找到对应的记录");
            }

            // 检查当前状态是否为已驳回(3)
            if (!"3".equals(aiDataExchangeMainInfo.getStatus())) {
                return AjaxResult.error("当前记录状态不是已驳回状态，无法进行重新提交操作");
            }

            // 保存原文件路径，用于后续删除
            String oldFilePath = aiDataExchangeMainInfo.getFilePath();

            // 调用通用方法处理文件上传和解析，保留原有的部门ID信息
            Map<String, Object> processResult = processExcelFile(file, userId, aiDataExchangeMainInfo.getDeptId(),
                    aiDataExchangeMainInfo.getFactoryId(), aiDataExchangeMainInfo.getIdsId(),
                    aiDataExchangeMainInfo.getFunctionId(), aiDataExchangeMainInfo, aiDataExchangeMainInfoId);

            // 检查是否有错误
            if (processResult.containsKey("error")) {
                return AjaxResult.error(processResult.get("error").toString());
            }

            // 获取处理结果
            aiDataExchangeMainInfo = (AiDataExchangeMainInfo) processResult.get("aiDataExchangeMainInfo");

            // 更新记录
            updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);

            // 删除旧文件
            if (StringUtils.isNotEmpty(oldFilePath)) {
                File oldFile = new File(oldFilePath);
                if (oldFile.exists()) {
                    oldFile.delete();
                }
            }

            // 返回成功信息和aiDataExchangeMainInfo对象
            return AjaxResult.success("重新提交成功").put("data", aiDataExchangeMainInfo);
        } catch (Exception e) {
            log.error("重新提交文件异常：", e);
            return AjaxResult.error("重新提交文件异常：" + e.getMessage());
        }
    }

    /**
     * 检查客户料号和送货单号组合是否已存在（排除当前记录）
     *
     * @param partNo 客户料号
     * @param asnNo 送货单号
     * @param currentId 当前记录ID
     * @return 是否存在
     */
    private boolean checkPartNoAndAsnNoExistsExcludeCurrent(String partNo, String asnNo, Long currentId) {
        AiDataExchangeMainInfo query = new AiDataExchangeMainInfo();
        query.setPartNo(partNo);
        query.setAsnNo(asnNo);
        List<AiDataExchangeMainInfo> list = aiDataExchangeMainInfoMapper.selectAiDataExchangeMainInfoList(query);

        if (list != null && !list.isEmpty()) {
            for (AiDataExchangeMainInfo info : list) {
                // 如果找到的记录不是当前记录，则表示存在重复
                if (!info.getAiDataExchangeMainInfoId().equals(currentId)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 根据用户昵称查询用户邮箱（通用方法）
     *
     * @param nickName 用户昵称
     * @return 用户邮箱，如果未找到则返回null
     */
    private String getUserEmail(String nickName,String actionType) {
        if (StringUtils.isEmpty(nickName)) {
            return null;
        }

        try {
            // 根据昵称查询用户信息
            List<SysUser> users = sysUserMapper.selectUserByNickName(nickName);
            if (users != null && !users.isEmpty()) {
                // 取第一个匹配的用户
                SysUser user = users.get(0);
                return user.getEmail();
            }
        } catch (Exception e) {
            log.error("查询用户邮箱失败，用户昵称：{}，错误信息：{}", nickName, e.getMessage());
        }

        return null;
    }


    /**
     * 发送提醒邮件（通用方法）
     *
     * @param userEmail 用户邮箱
     * @param aiDataExchangeMainInfo 数据交换记录信息
     * @param actionType 操作类型（复核、核定）
     * @param userName 用户名称
     */
    private void sendNotificationEmail(String userEmail, AiDataExchangeMainInfo aiDataExchangeMainInfo, String actionType, String userName) {
        if (StringUtils.isEmpty(userEmail)) {
            log.warn("{}人邮箱为空，无法发送提醒邮件", actionType);
            return;
        }

        try {
            String subject = String.format("质检报告待%s提醒", actionType);
            String body = String.format(
                "您好，有一份质检报告需要您%s：\n\n" +
                "文件名：%s\n" +
                "送货单号：%s\n" +
                "客户料号：%s\n" +
                "客户名称：%s\n" +
                "上传时间：%s\n\n" +
                "请及时登录系统进行%s。\n\n" +
                "此邮件为系统自动发送，请勿回复。",
                actionType,
                aiDataExchangeMainInfo.getFileName(),
                aiDataExchangeMainInfo.getAsnNo(),
                aiDataExchangeMainInfo.getPartNo(),
                aiDataExchangeMainInfo.getCustomerName(),
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(aiDataExchangeMainInfo.getCreateTime()),
                actionType
            );

            // 调用邮件服务发送邮件
            emailService.selectEmail(userEmail, subject, body, "");
            log.info("成功发送提醒邮件给{}人：{}，邮箱：{}", actionType, userName, userEmail);

        } catch (Exception e) {
            log.error("发送提醒邮件失败，{}人邮箱：{}，错误信息：{}", actionType, userEmail, e.getMessage());
        }
    }

    /**
     * 复核人复核操作
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 处理结果
     */
    @Override
    public AjaxResult checkRecord(Long aiDataExchangeMainInfoId) {
        try {
            // 查询当前记录
            AiDataExchangeMainInfo aiDataExchangeMainInfo = selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId);
            if (aiDataExchangeMainInfo == null) {
                return AjaxResult.error("未找到对应的记录");
            }

            // 获取当前登录用户
            String currentUserNickName = SecurityUtils.getLoginUser().getUser().getNickName();

            // 验证当前用户是否为记录中的复核人
            if (!currentUserNickName.equals(aiDataExchangeMainInfo.getCheckedBy())) {
                return AjaxResult.error("无法修改不属于自己的记录，当前记录的复核人为：" + aiDataExchangeMainInfo.getCheckedBy());
            }

            // 修改状态为待核定(1)
            aiDataExchangeMainInfo.setStatus("1");

            // 更新记录
            int result = updateAiDataExchangeMainInfo(aiDataExchangeMainInfo);
            if (result > 0) {
                // 发送提醒邮件给核定人
                String approverEmail = getUserEmail(aiDataExchangeMainInfo.getApprovedBy(),"approvedBy");
                if (StringUtils.isNotEmpty(approverEmail)) {
                    sendNotificationEmail(approverEmail, aiDataExchangeMainInfo, "核定", aiDataExchangeMainInfo.getApprovedBy());
                } else {
                    log.warn("未找到核定人邮箱，核定人昵称：{}", aiDataExchangeMainInfo.getApprovedBy());
                }
                return AjaxResult.success("复核成功，已通知核定人");
            } else {
                return AjaxResult.error("复核失败");
            }

        } catch (Exception e) {
            log.error("复核操作失败：", e);
            return AjaxResult.error("复核操作失败：" + e.getMessage());
        }
    }
}
