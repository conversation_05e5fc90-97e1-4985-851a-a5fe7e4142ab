package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeMainInfo;

/**
 * AI数据交换Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface AiDataExchangeMainInfoMapper 
{
    /**
     * 查询AI数据交换
     * 
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return AI数据交换
     */
    public AiDataExchangeMainInfo selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId);

    /**
     * 查询AI数据交换列表
     * 
     * @param aiDataExchangeMainInfo AI数据交换
     * @return AI数据交换集合
     */
    public List<AiDataExchangeMainInfo> selectAiDataExchangeMainInfoList(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 新增AI数据交换
     * 
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    public int insertAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 修改AI数据交换
     * 
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    public int updateAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 删除AI数据交换
     * 
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 结果
     */
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId);

    /**
     * 批量删除AI数据交换
     * 
     * @param aiDataExchangeMainInfoIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds(Long[] aiDataExchangeMainInfoIds);
}
