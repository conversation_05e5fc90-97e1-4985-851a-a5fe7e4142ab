package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeApprovalMapping;
import com.ruoyi.system.domain.dto.ApprovalUserDTO;

/**
 * 签核人员映射Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface AiDataExchangeApprovalMappingMapper
{
    /**
     * 查询签核人员映射
     *
     * @param aiDataExchangeMakerPartNoId 签核人员映射主键
     * @param userId 签核人员ID
     * @return 签核人员映射
     */
    public AiDataExchangeApprovalMapping selectAiDataExchangeApprovalMappingByIds(Long aiDataExchangeMakerPartNoId, Long userId);

    /**
     * 查询签核人员映射列表
     *
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 签核人员映射集合
     */
    public List<AiDataExchangeApprovalMapping> selectAiDataExchangeApprovalMappingList(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping);

    /**
     * 新增签核人员映射
     *
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 结果
     */
    public int insertAiDataExchangeApprovalMapping(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping);

    /**
     * 修改签核人员映射
     *
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 结果
     */
    public int updateAiDataExchangeApprovalMapping(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping);

    /**
     * 删除签核人员映射
     *
     * @param aiDataExchangeMakerPartNoId 签核人员映射主键
     * @param userId 签核人员ID
     * @return 结果
     */
    public int deleteAiDataExchangeApprovalMappingByIds(Long aiDataExchangeMakerPartNoId, Long userId);

    /**
     * 批量删除签核人员映射
     *
     * @param aiDataExchangeMakerPartNoIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeApprovalMappingByMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds);

    /**
     * 批量删除签核人员映射
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeApprovalMappingByUserIds(Long[] userIds);

    /**
     * 根据maker料号ID查询签核人员信息
     *
     * @param aiDataExchangeMakerPartNoId maker料号ID
     * @return 签核人员信息列表
     */
    public List<ApprovalUserDTO> selectApprovalUsersByMakerPartNoId(Long aiDataExchangeMakerPartNoId);
}
