<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DataFileMapper">
    
    <resultMap type="DataFile" id="DataFileResult">
        <result property="asnNo"    column="asn_no"    />
        <result property="partNo"    column="part_no"    />
        <result property="makerPartNo"    column="maker_part_no"    />
    </resultMap>

    <sql id="selectDataFileVo">
        select asn_no, part_no, maker_part_no from data_file
    </sql>

    <select id="selectDataFileList" parameterType="DataFile" resultMap="DataFileResult">
        <include refid="selectDataFileVo"/>
        <where>  
            <if test="asnNo != null  and asnNo != ''"> and asn_no = #{asnNo}</if>
            <if test="partNo != null  and partNo != ''"> and part_no = #{partNo}</if>
            <if test="makerPartNo != null  and makerPartNo != ''"> and maker_part_no = #{makerPartNo}</if>
        </where>
    </select>
    
    <select id="selectDataFileByAsnNo" parameterType="String" resultMap="DataFileResult">
        <include refid="selectDataFileVo"/>
        where asn_no = #{asnNo}
    </select>

    <insert id="insertDataFile" parameterType="DataFile">
        insert into data_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="asnNo != null">asn_no,</if>
            <if test="partNo != null">part_no,</if>
            <if test="makerPartNo != null">maker_part_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="asnNo != null">#{asnNo},</if>
            <if test="partNo != null">#{partNo},</if>
            <if test="makerPartNo != null">#{makerPartNo},</if>
         </trim>
    </insert>

    <update id="updateDataFile" parameterType="DataFile">
        update data_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="partNo != null">part_no = #{partNo},</if>
            <if test="makerPartNo != null">maker_part_no = #{makerPartNo},</if>
        </trim>
        where asn_no = #{asnNo}
    </update>

    <delete id="deleteDataFileByAsnNo" parameterType="String">
        delete from data_file where asn_no = #{asnNo}
    </delete>

    <delete id="deleteDataFileByAsnNos" parameterType="String">
        delete from data_file where asn_no in 
        <foreach item="asnNo" collection="array" open="(" separator="," close=")">
            #{asnNo}
        </foreach>
    </delete>
</mapper>