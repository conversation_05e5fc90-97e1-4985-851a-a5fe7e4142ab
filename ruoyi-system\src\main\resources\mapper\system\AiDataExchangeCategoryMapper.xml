<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeCategoryMapper">

    <resultMap type="AiDataExchangeCategory" id="AiDataExchangeCategoryResult">
        <result property="aiDataExchangeCategoryId"    column="ai_data_exchange_category_id"    />
        <result property="categoryName"    column="category_name"    />
    </resultMap>

    <sql id="selectAiDataExchangeCategoryVo">
        select ai_data_exchange_category_id, category_name from ai_data_exchange_category
    </sql>

    <select id="selectAiDataExchangeCategoryList" parameterType="AiDataExchangeCategory" resultMap="AiDataExchangeCategoryResult">
        <include refid="selectAiDataExchangeCategoryVo"/>
        <where>
            <if test="aiDataExchangeCategoryId != null "> and ai_data_exchange_category_id = #{aiDataExchangeCategoryId}</if>
            <if test="categoryName != null  and categoryName != ''"> and UPPER(REPLACE(category_name, ' ', '')) = #{categoryName}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeCategoryByAiDataExchangeCategoryId" parameterType="Long" resultMap="AiDataExchangeCategoryResult">
        <include refid="selectAiDataExchangeCategoryVo"/>
        where ai_data_exchange_category_id = #{aiDataExchangeCategoryId}
    </select>

    <insert id="insertAiDataExchangeCategory" parameterType="AiDataExchangeCategory">
        insert into ai_data_exchange_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeCategoryId != null">ai_data_exchange_category_id,</if>
            <if test="categoryName != null">category_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeCategoryId != null">#{aiDataExchangeCategoryId},</if>
            <if test="categoryName != null">#{categoryName},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeCategory" parameterType="AiDataExchangeCategory">
        update ai_data_exchange_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null">category_name = #{categoryName},</if>
        </trim>
        where ai_data_exchange_category_id = #{aiDataExchangeCategoryId}
    </update>

    <delete id="deleteAiDataExchangeCategoryByAiDataExchangeCategoryId" parameterType="Long">
        delete from ai_data_exchange_category where ai_data_exchange_category_id = #{aiDataExchangeCategoryId}
    </delete>

    <delete id="deleteAiDataExchangeCategoryByAiDataExchangeCategoryIds" parameterType="String">
        delete from ai_data_exchange_category where ai_data_exchange_category_id in
        <foreach item="aiDataExchangeCategoryId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeCategoryId}
        </foreach>
    </delete>
</mapper>
