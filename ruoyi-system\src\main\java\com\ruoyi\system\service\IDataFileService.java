package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.DataFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * 保存上傳文件的信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IDataFileService 
{
    /**
     * 查询保存上傳文件的信息
     * 
     * @param asnNo 保存上傳文件的信息主键
     * @return 保存上傳文件的信息
     */
    public DataFile selectDataFileByAsnNo(String asnNo);

    /**
     * 查询保存上傳文件的信息列表
     * 
     * @param dataFile 保存上傳文件的信息
     * @return 保存上傳文件的信息集合
     */
    public List<DataFile> selectDataFileList(DataFile dataFile);

    /**
     * 新增保存上傳文件的信息
     * 
     * @param dataFile 保存上傳文件的信息
     * @return 结果
     */
    public int insertDataFile(DataFile dataFile);

    /**
     * 修改保存上傳文件的信息
     * 
     * @param dataFile 保存上傳文件的信息
     * @return 结果
     */
    public int updateDataFile(DataFile dataFile);

    /**
     * 批量删除保存上傳文件的信息
     * 
     * @param asnNos 需要删除的保存上傳文件的信息主键集合
     * @return 结果
     */
    public int deleteDataFileByAsnNos(String[] asnNos);

    /**
     * 删除保存上傳文件的信息信息
     * 
     * @param asnNo 保存上傳文件的信息主键
     * @return 结果
     */
    public int deleteDataFileByAsnNo(String asnNo);

    /**
     * 接收上传的文件并进行抛转
     * @param files
     */
    void receiveFile(MultipartFile[] files);
}
