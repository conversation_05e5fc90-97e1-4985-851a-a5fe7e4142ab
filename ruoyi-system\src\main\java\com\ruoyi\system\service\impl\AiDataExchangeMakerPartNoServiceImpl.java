package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeMakerPartNoMapper;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;
import com.ruoyi.system.service.IAiDataExchangeMakerPartNoService;

/**
 * AI数据交换的maker料号信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeMakerPartNoServiceImpl implements IAiDataExchangeMakerPartNoService 
{
    @Autowired
    private AiDataExchangeMakerPartNoMapper aiDataExchangeMakerPartNoMapper;

    /**
     * 查询AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号信息主键
     * @return AI数据交换的maker料号信息
     */
    @Override
    public AiDataExchangeMakerPartNo selectAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId)
    {
        return aiDataExchangeMakerPartNoMapper.selectAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(aiDataExchangeMakerPartNoId);
    }

    /**
     * 查询AI数据交换的maker料号信息列表
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return AI数据交换的maker料号信息
     */
    @Override
    public List<AiDataExchangeMakerPartNo> selectAiDataExchangeMakerPartNoList(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo)
    {
        return aiDataExchangeMakerPartNoMapper.selectAiDataExchangeMakerPartNoList(aiDataExchangeMakerPartNo);
    }

    /**
     * 新增AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeMakerPartNo(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo)
    {
        return aiDataExchangeMakerPartNoMapper.insertAiDataExchangeMakerPartNo(aiDataExchangeMakerPartNo);
    }

    /**
     * 修改AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeMakerPartNo(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo)
    {
        return aiDataExchangeMakerPartNoMapper.updateAiDataExchangeMakerPartNo(aiDataExchangeMakerPartNo);
    }

    /**
     * 批量删除AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNoIds 需要删除的AI数据交换的maker料号信息主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds)
    {
        return aiDataExchangeMakerPartNoMapper.deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoIds(aiDataExchangeMakerPartNoIds);
    }

    /**
     * 删除AI数据交换的maker料号信息信息
     * 
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号信息主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId)
    {
        return aiDataExchangeMakerPartNoMapper.deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(aiDataExchangeMakerPartNoId);
    }
}
