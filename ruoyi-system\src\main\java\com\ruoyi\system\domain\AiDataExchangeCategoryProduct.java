package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换的产品类别和产品的关联对象 ai_data_exchange_category_product
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeCategoryProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换的产品类别ID */
    @Excel(name = "产品类别ID")
    private Long aiDataExchangeCategoryId;

    /** AI数据交换的产品ID */
    @Excel(name = "产品ID")
    private Long aiDataExchangeProductId;

    public void setAiDataExchangeCategoryId(Long aiDataExchangeCategoryId) 
    {
        this.aiDataExchangeCategoryId = aiDataExchangeCategoryId;
    }

    public Long getAiDataExchangeCategoryId() 
    {
        return aiDataExchangeCategoryId;
    }

    public void setAiDataExchangeProductId(Long aiDataExchangeProductId) 
    {
        this.aiDataExchangeProductId = aiDataExchangeProductId;
    }

    public Long getAiDataExchangeProductId() 
    {
        return aiDataExchangeProductId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeCategoryId", getAiDataExchangeCategoryId())
            .append("aiDataExchangeProductId", getAiDataExchangeProductId())
            .toString();
    }
}
