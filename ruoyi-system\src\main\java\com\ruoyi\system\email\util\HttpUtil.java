package com.ruoyi.system.email.util;


import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;

public class HttpUtil {
    private static final String USER_AGENT = "user-agent";
    private static final String USER_AGENT_VALUE = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)";
    private static final String CONNECTION = "connection";
    private static final String CONNECTION_VALUE = "Keep-Alive";
    private static final String ACCEPT = "accept";
    private static final String UTF8 = "utf-8";
    private static final String ACCEPT_CHARSET = "Accept-Charset";
    private static final String CONTENTTYPE = "contentType";


    public HttpUtil() throws IOException {
        String url = "http://10.196.7.39:7209/api/MailSend/PostMail_Send";
        String ls_to ="<EMAIL>";    //emailMapper.selectEmail(workId);
        String ls_from = "<EMAIL>";
        String ls_cc="";
        String ls_subject="<<<无纸化专案----->重工记录表需要您签核 ";
        String ls_body="HTTP://10.196.5.230:220"+"\\n"+"当前流程为:生產線長(請您簽核)-->生產課長--->QC---->責任單位---->QC";
        sendPost(url,ls_to,ls_cc,ls_subject,ls_body);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param ls_to 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */

 public static String sendPost(String url, String ls_to,String ls_cc,
                                String ls_subject,String ls_body) throws IOException {
        StringBuilder result = new StringBuilder();
        String urlNameString = url  + ls_to +ls_cc +ls_subject+ls_body;
        URL realUrl = new URL(urlNameString);
        URLConnection conn = realUrl.openConnection();
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setRequestProperty(CONTENTTYPE, UTF8);
        conn.setRequestProperty(ACCEPT_CHARSET, UTF8);
        conn.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
        conn.setRequestProperty(CONNECTION, CONNECTION_VALUE);
        conn.setRequestProperty(ACCEPT, "*/*");
        try (PrintWriter out = new PrintWriter(conn.getOutputStream());
             BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(),StandardCharsets.UTF_8))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            out.flush();
            out.print(ls_to+ls_cc+ls_subject+ls_body);
        } catch (Exception e) {

        }
        System.out.println(result.toString());
        return result.toString();
    }

}

