<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeProductMakerPartNoMapper">

    <resultMap type="AiDataExchangeProductMakerPartNo" id="AiDataExchangeProductMakerPartNoResult">
        <result property="aiDataExchangeProductId"    column="ai_data_exchange_product_id"    />
        <result property="aiDataExchangeMakerPartNoId"    column="ai_data_exchange_maker_part_no_id"    />
    </resultMap>

    <sql id="selectAiDataExchangeProductMakerPartNoVo">
        select ai_data_exchange_product_id, ai_data_exchange_maker_part_no_id from ai_data_exchange_product_maker_part_no
    </sql>

    <select id="selectAiDataExchangeProductMakerPartNoList" parameterType="AiDataExchangeProductMakerPartNo" resultMap="AiDataExchangeProductMakerPartNoResult">
        <include refid="selectAiDataExchangeProductMakerPartNoVo"/>
        <where>
            <if test="aiDataExchangeProductId != null "> and ai_data_exchange_product_id = #{aiDataExchangeProductId}</if>
            <if test="aiDataExchangeMakerPartNoId != null "> and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeProductMakerPartNoByIds" resultMap="AiDataExchangeProductMakerPartNoResult">
        <include refid="selectAiDataExchangeProductMakerPartNoVo"/>
        where ai_data_exchange_product_id = #{aiDataExchangeProductId} and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </select>

    <insert id="insertAiDataExchangeProductMakerPartNo" parameterType="AiDataExchangeProductMakerPartNo">
        insert into ai_data_exchange_product_maker_part_no
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id,</if>
            <if test="aiDataExchangeMakerPartNoId != null">ai_data_exchange_maker_part_no_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeProductId != null">#{aiDataExchangeProductId},</if>
            <if test="aiDataExchangeMakerPartNoId != null">#{aiDataExchangeMakerPartNoId},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeProductMakerPartNo" parameterType="AiDataExchangeProductMakerPartNo">
        update ai_data_exchange_product_maker_part_no
        <trim prefix="SET" suffixOverrides=",">
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id = #{aiDataExchangeProductId},</if>
            <if test="aiDataExchangeMakerPartNoId != null">ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId},</if>
        </trim>
        where ai_data_exchange_product_id = #{aiDataExchangeProductId} and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </update>

    <delete id="deleteAiDataExchangeProductMakerPartNoByIds">
        delete from ai_data_exchange_product_maker_part_no where ai_data_exchange_product_id = #{aiDataExchangeProductId} and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </delete>

    <delete id="deleteAiDataExchangeProductMakerPartNoByProductIds" parameterType="String">
        delete from ai_data_exchange_product_maker_part_no where ai_data_exchange_product_id in
        <foreach item="aiDataExchangeProductId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeProductId}
        </foreach>
    </delete>

    <delete id="deleteAiDataExchangeProductMakerPartNoByMakerPartNoIds" parameterType="String">
        delete from ai_data_exchange_product_maker_part_no where ai_data_exchange_maker_part_no_id in
        <foreach item="aiDataExchangeMakerPartNoId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeMakerPartNoId}
        </foreach>
    </delete>

    <resultMap type="AiDataExchangeMakerPartNo" id="AiDataExchangeMakerPartNoResult">
        <result property="aiDataExchangeMakerPartNoId"    column="ai_data_exchange_maker_part_no_id"    />
        <result property="makerPartNo"    column="maker_part_no"    />
    </resultMap>

    <select id="selectMakerPartNosByProductId" parameterType="Long" resultMap="AiDataExchangeMakerPartNoResult">
        select m.ai_data_exchange_maker_part_no_id, m.maker_part_no
        from ai_data_exchange_maker_part_no m
        inner join ai_data_exchange_product_maker_part_no pm on m.ai_data_exchange_maker_part_no_id = pm.ai_data_exchange_maker_part_no_id
        where pm.ai_data_exchange_product_id = #{productId}
    </select>
</mapper>
