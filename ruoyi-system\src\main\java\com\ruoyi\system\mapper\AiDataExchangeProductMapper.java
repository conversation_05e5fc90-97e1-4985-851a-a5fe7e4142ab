package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeProduct;

/**
 * AI数据交换的产品信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface AiDataExchangeProductMapper 
{
    /**
     * 查询AI数据交换的产品信息
     * 
     * @param aiDataExchangeProductId AI数据交换的产品信息主键
     * @return AI数据交换的产品信息
     */
    public AiDataExchangeProduct selectAiDataExchangeProductByAiDataExchangeProductId(Long aiDataExchangeProductId);

    /**
     * 查询AI数据交换的产品信息列表
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return AI数据交换的产品信息集合
     */
    public List<AiDataExchangeProduct> selectAiDataExchangeProductList(AiDataExchangeProduct aiDataExchangeProduct);

    /**
     * 新增AI数据交换的产品信息
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return 结果
     */
    public int insertAiDataExchangeProduct(AiDataExchangeProduct aiDataExchangeProduct);

    /**
     * 修改AI数据交换的产品信息
     * 
     * @param aiDataExchangeProduct AI数据交换的产品信息
     * @return 结果
     */
    public int updateAiDataExchangeProduct(AiDataExchangeProduct aiDataExchangeProduct);

    /**
     * 删除AI数据交换的产品信息
     * 
     * @param aiDataExchangeProductId AI数据交换的产品信息主键
     * @return 结果
     */
    public int deleteAiDataExchangeProductByAiDataExchangeProductId(Long aiDataExchangeProductId);

    /**
     * 批量删除AI数据交换的产品信息
     * 
     * @param aiDataExchangeProductIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeProductByAiDataExchangeProductIds(Long[] aiDataExchangeProductIds);
}
