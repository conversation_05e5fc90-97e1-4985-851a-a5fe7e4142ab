package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;
import com.ruoyi.system.domain.AiDataExchangeProductMakerPartNo;
import org.apache.ibatis.annotations.Param;

/**
 * AI数据交换的产品与maker料号的关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface AiDataExchangeProductMakerPartNoMapper
{
    /**
     * 查询AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号ID
     * @return AI数据交换的产品与maker料号的关联
     */
    public AiDataExchangeProductMakerPartNo selectAiDataExchangeProductMakerPartNoByIds(@Param("aiDataExchangeProductId") Long aiDataExchangeProductId,@Param("aiDataExchangeMakerPartNoId") Long aiDataExchangeMakerPartNoId);

    /**
     * 查询AI数据交换的产品与maker料号的关联列表
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return AI数据交换的产品与maker料号的关联集合
     */
    public List<AiDataExchangeProductMakerPartNo> selectAiDataExchangeProductMakerPartNoList(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo);

    /**
     * 新增AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return 结果
     */
    public int insertAiDataExchangeProductMakerPartNo(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo);

    /**
     * 修改AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return 结果
     */
    public int updateAiDataExchangeProductMakerPartNo(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo);

    /**
     * 删除AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号ID
     * @return 结果
     */
    public int deleteAiDataExchangeProductMakerPartNoByIds(Long aiDataExchangeProductId, Long aiDataExchangeMakerPartNoId);

    /**
     * 批量删除AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeProductMakerPartNoByProductIds(Long[] aiDataExchangeProductIds);

    /**
     * 批量删除AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeMakerPartNoIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeProductMakerPartNoByMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds);

    /**
     * 根据产品ID查询maker料号信息
     *
     * @param productId 产品ID
     * @return maker料号信息列表
     */
    public List<AiDataExchangeMakerPartNo> selectMakerPartNosByProductId(Long productId);
}
