package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换的maker料号信息对象 ai_data_exchange_maker_part_no
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeMakerPartNo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换的maker料号ID */
    private Long aiDataExchangeMakerPartNoId;

    /** maker料号 */
    @Excel(name = "maker料号")
    private String makerPartNo;

    public void setAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId) 
    {
        this.aiDataExchangeMakerPartNoId = aiDataExchangeMakerPartNoId;
    }

    public Long getAiDataExchangeMakerPartNoId() 
    {
        return aiDataExchangeMakerPartNoId;
    }

    public void setMakerPartNo(String makerPartNo) 
    {
        this.makerPartNo = makerPartNo;
    }

    public String getMakerPartNo() 
    {
        return makerPartNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeMakerPartNoId", getAiDataExchangeMakerPartNoId())
            .append("makerPartNo", getMakerPartNo())
            .toString();
    }
}
