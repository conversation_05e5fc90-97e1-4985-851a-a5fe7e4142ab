package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeProductMakerPartNoMapper;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;
import com.ruoyi.system.domain.AiDataExchangeProductMakerPartNo;
import com.ruoyi.system.service.IAiDataExchangeProductMakerPartNoService;

/**
 * AI数据交换的产品与maker料号的关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeProductMakerPartNoServiceImpl implements IAiDataExchangeProductMakerPartNoService
{
    @Autowired
    private AiDataExchangeProductMakerPartNoMapper aiDataExchangeProductMakerPartNoMapper;

    /**
     * 查询AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号ID
     * @return AI数据交换的产品与maker料号的关联
     */
    @Override
    public AiDataExchangeProductMakerPartNo selectAiDataExchangeProductMakerPartNoByIds(Long aiDataExchangeProductId, Long aiDataExchangeMakerPartNoId)
    {
        return aiDataExchangeProductMakerPartNoMapper.selectAiDataExchangeProductMakerPartNoByIds(aiDataExchangeProductId, aiDataExchangeMakerPartNoId);
    }

    /**
     * 查询AI数据交换的产品与maker料号的关联列表
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return AI数据交换的产品与maker料号的关联
     */
    @Override
    public List<AiDataExchangeProductMakerPartNo> selectAiDataExchangeProductMakerPartNoList(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo)
    {
        return aiDataExchangeProductMakerPartNoMapper.selectAiDataExchangeProductMakerPartNoList(aiDataExchangeProductMakerPartNo);
    }

    /**
     * 新增AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeProductMakerPartNo(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo)
    {
        return aiDataExchangeProductMakerPartNoMapper.insertAiDataExchangeProductMakerPartNo(aiDataExchangeProductMakerPartNo);
    }

    /**
     * 修改AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductMakerPartNo AI数据交换的产品与maker料号的关联
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeProductMakerPartNo(AiDataExchangeProductMakerPartNo aiDataExchangeProductMakerPartNo)
    {
        return aiDataExchangeProductMakerPartNoMapper.updateAiDataExchangeProductMakerPartNo(aiDataExchangeProductMakerPartNo);
    }

    /**
     * 批量删除AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeProductIds 需要删除的AI数据交换的产品ID集合
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeProductMakerPartNoByProductIds(Long[] aiDataExchangeProductIds)
    {
        return aiDataExchangeProductMakerPartNoMapper.deleteAiDataExchangeProductMakerPartNoByProductIds(aiDataExchangeProductIds);
    }

    /**
     * 批量删除AI数据交换的产品与maker料号的关联
     *
     * @param aiDataExchangeMakerPartNoIds 需要删除的AI数据交换的maker料号ID集合
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeProductMakerPartNoByMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds)
    {
        return aiDataExchangeProductMakerPartNoMapper.deleteAiDataExchangeProductMakerPartNoByMakerPartNoIds(aiDataExchangeMakerPartNoIds);
    }

    /**
     * 删除AI数据交换的产品与maker料号的关联信息
     *
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号ID
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeProductMakerPartNoByIds(Long aiDataExchangeProductId, Long aiDataExchangeMakerPartNoId)
    {
        return aiDataExchangeProductMakerPartNoMapper.deleteAiDataExchangeProductMakerPartNoByIds(aiDataExchangeProductId, aiDataExchangeMakerPartNoId);
    }

    /**
     * 根据产品ID查询maker料号信息
     *
     * @param productId 产品ID
     * @return maker料号信息列表
     */
    @Override
    public List<AiDataExchangeMakerPartNo> selectMakerPartNosByProductId(Long productId)
    {
        return aiDataExchangeProductMakerPartNoMapper.selectMakerPartNosByProductId(productId);
    }
}
