package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeCategoryProduct;
import com.ruoyi.system.domain.AiDataExchangeProduct;
import org.apache.ibatis.annotations.Param;

/**
 * AI数据交换的产品类别和产品的关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface AiDataExchangeCategoryProductMapper
{
    /**
     * 查询AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryId AI数据交换的产品类别ID
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @return AI数据交换的产品类别和产品的关联
     */
    public AiDataExchangeCategoryProduct selectAiDataExchangeCategoryProductByIds(@Param("aiDataExchangeCategoryId") Long aiDataExchangeCategoryId,@Param("aiDataExchangeProductId") Long aiDataExchangeProductId);

    /**
     * 查询AI数据交换的产品类别和产品的关联列表
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return AI数据交换的产品类别和产品的关联集合
     */
    public List<AiDataExchangeCategoryProduct> selectAiDataExchangeCategoryProductList(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct);

    /**
     * 新增AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return 结果
     */
    public int insertAiDataExchangeCategoryProduct(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct);

    /**
     * 修改AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryProduct AI数据交换的产品类别和产品的关联
     * @return 结果
     */
    public int updateAiDataExchangeCategoryProduct(AiDataExchangeCategoryProduct aiDataExchangeCategoryProduct);

    /**
     * 删除AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryId AI数据交换的产品类别ID
     * @param aiDataExchangeProductId AI数据交换的产品ID
     * @return 结果
     */
    public int deleteAiDataExchangeCategoryProductByIds(Long aiDataExchangeCategoryId, Long aiDataExchangeProductId);

    /**
     * 批量删除AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeCategoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeCategoryProductByCategoryIds(Long[] aiDataExchangeCategoryIds);

    /**
     * 批量删除AI数据交换的产品类别和产品的关联
     *
     * @param aiDataExchangeProductIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeCategoryProductByProductIds(Long[] aiDataExchangeProductIds);

    /**
     * 根据产品类别ID查询产品信息
     *
     * @param categoryId 产品类别ID
     * @return 产品信息列表
     */
    public List<AiDataExchangeProduct> selectProductsByCategoryId(Long categoryId);
}
