package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换对象 ai_data_exchange_main_info
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeMainInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换主表ID */
    private Long aiDataExchangeMainInfoId;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String fileName;

    /** 文件保存在电脑的路径，包括随机生成的文件名 */
    @Excel(name = "文件保存在电脑的路径，包括随机生成的文件名")
    private String filePath;

    /** 审核状态（0待复核 1待核定 2已完成 3已驳回） */
    @Excel(name = "审核状态", readConverterExp = "0=待复核,1=待核定,2=已完成,3=已驳回")
    private String status;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    private String rejectReason;

    /** 上传人员（检验员）ID */
    @Excel(name = "上传人员ID")
    private Long inspectedById;

    /** 上传人员（检验员）名 */
    @Excel(name = "上传人员名")
    private String inspectedBy;

    /** 复核人名 */
    @Excel(name = "复核人名")
    private String checkedBy;

    /** 核定人名 */
    @Excel(name = "核定人名")
    private String approvedBy;

    /** 厂区id */
    @Excel(name = "厂区id")
    private Long factoryId;

    /** 厂区名称 */
    @Excel(name = "厂区名称")
    private String factory;

    /** 产品处IDS id */
    @Excel(name = "产品处IDS id")
    private Long idsId;

    /** 产品处IDS名称 */
    @Excel(name = "产品处IDS名称")
    private String ids;

    /** 机能id */
    @Excel(name = "机能id")
    private Long functionId;

    /** 机能名称 */
    @Excel(name = "机能名称")
    private String function;

    /** 课别id */
    @Excel(name = "课别id")
    private Long deptId;

    /** 课别名称 */
    @Excel(name = "课别名称")
    private String section;

    /** 产品类别ID */
    @Excel(name = "产品类别ID")
    private Long aiDataExchangeCategoryId;

    /** 产品ID */
    @Excel(name = "产品ID")
    private Long aiDataExchangeProductId;

    /** maker（厂内）料号id */
    @Excel(name = "maker")
    private Long makerPartNoId;

    /** 送货单号 */
    @Excel(name = "送货单号")
    private String asnNo;

    /** 客户料号 */
    @Excel(name = "客户料号")
    private String partNo;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 产品类别名称 */
    @Excel(name = "产品类别名称")
    private String categoryName;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 厂内料号 */
    @Excel(name = "厂内料号")
    private String makerPartNo;

    public void setAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId)
    {
        this.aiDataExchangeMainInfoId = aiDataExchangeMainInfoId;
    }

    public Long getAiDataExchangeMainInfoId()
    {
        return aiDataExchangeMainInfoId;
    }

    public void setFileName(String fileName)
    {
        this.fileName = fileName;
    }

    public String getFileName()
    {
        return fileName;
    }

    public void setFilePath(String filePath)
    {
        this.filePath = filePath;
    }

    public String getFilePath()
    {
        return filePath;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setRejectReason(String rejectReason)
    {
        this.rejectReason = rejectReason;
    }

    public String getRejectReason()
    {
        return rejectReason;
    }

    public void setInspectedById(Long inspectedById)
    {
        this.inspectedById = inspectedById;
    }

    public Long getInspectedById()
    {
        return inspectedById;
    }

    public void setInspectedBy(String inspectedBy)
    {
        this.inspectedBy = inspectedBy;
    }

    public String getInspectedBy()
    {
        return inspectedBy;
    }

    public void setCheckedBy(String checkedBy)
    {
        this.checkedBy = checkedBy;
    }

    public String getCheckedBy()
    {
        return checkedBy;
    }

    public void setApprovedBy(String approvedBy)
    {
        this.approvedBy = approvedBy;
    }

    public String getApprovedBy()
    {
        return approvedBy;
    }

    public void setFactoryId(Long factoryId)
    {
        this.factoryId = factoryId;
    }

    public Long getFactoryId()
    {
        return factoryId;
    }

    public void setFactory(String factory)
    {
        this.factory = factory;
    }

    public String getFactory()
    {
        return factory;
    }

    public void setIdsId(Long idsId)
    {
        this.idsId = idsId;
    }

    public Long getIdsId()
    {
        return idsId;
    }

    public void setIds(String ids)
    {
        this.ids = ids;
    }

    public String getIds()
    {
        return ids;
    }

    public void setFunctionId(Long functionId)
    {
        this.functionId = functionId;
    }

    public Long getFunctionId()
    {
        return functionId;
    }

    public void setFunction(String function)
    {
        this.function = function;
    }

    public String getFunction()
    {
        return function;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setSection(String section)
    {
        this.section = section;
    }

    public String getSection()
    {
        return section;
    }

    public void setAiDataExchangeCategoryId(Long aiDataExchangeCategoryId)
    {
        this.aiDataExchangeCategoryId = aiDataExchangeCategoryId;
    }

    public Long getAiDataExchangeCategoryId()
    {
        return aiDataExchangeCategoryId;
    }

    public void setAiDataExchangeProductId(Long aiDataExchangeProductId)
    {
        this.aiDataExchangeProductId = aiDataExchangeProductId;
    }

    public Long getAiDataExchangeProductId()
    {
        return aiDataExchangeProductId;
    }

    public void setMakerPartNoId(Long makerPartNoId)
    {
        this.makerPartNoId = makerPartNoId;
    }

    public Long getMakerPartNoId()
    {
        return makerPartNoId;
    }

    public void setAsnNo(String asnNo)
    {
        this.asnNo = asnNo;
    }

    public String getAsnNo()
    {
        return asnNo;
    }

    public void setPartNo(String partNo)
    {
        this.partNo = partNo;
    }

    public String getPartNo()
    {
        return partNo;
    }

    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

    public String getCustomerName()
    {
        return customerName;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductName()
    {
        return productName;
    }

    public void setMakerPartNo(String makerPartNo)
    {
        this.makerPartNo = makerPartNo;
    }

    public String getMakerPartNo()
    {
        return makerPartNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeMainInfoId", getAiDataExchangeMainInfoId())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("status", getStatus())
            .append("rejectReason", getRejectReason())
            .append("inspectedById", getInspectedById())
            .append("inspectedBy", getInspectedBy())
            .append("checkedBy", getCheckedBy())
            .append("approvedBy", getApprovedBy())
            .append("factoryId", getFactoryId())
            .append("factory", getFactory())
            .append("idsId", getIdsId())
            .append("ids", getIds())
            .append("functionId", getFunctionId())
            .append("function", getFunction())
            .append("deptId", getDeptId())
            .append("section", getSection())
            .append("aiDataExchangeCategoryId", getAiDataExchangeCategoryId())
            .append("aiDataExchangeProductId", getAiDataExchangeProductId())
            .append("makerPartNoId", getMakerPartNoId())
            .append("asnNo", getAsnNo())
            .append("partNo", getPartNo())
            .append("customerName", getCustomerName())
            .append("categoryName", getCategoryName())
            .append("productName", getProductName())
            .append("makerPartNo", getMakerPartNo())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
