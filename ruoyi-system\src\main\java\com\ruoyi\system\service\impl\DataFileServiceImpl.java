package com.ruoyi.system.service.impl;

import java.io.File;
import java.util.List;
import java.util.UUID;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.DataFileMapper;
import com.ruoyi.system.domain.DataFile;
import com.ruoyi.system.domain.mitac.MiTACQualityReportRequest;
import com.ruoyi.system.domain.mitac.MiTACQualityReportResponse;
import com.ruoyi.system.service.IDataFileService;
import com.ruoyi.system.utils.ExcelParser;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 保存上傳文件的信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class DataFileServiceImpl implements IDataFileService
{

    private static final Logger log = LoggerFactory.getLogger(DataFileServiceImpl.class);

    @Autowired
    private DataFileMapper dataFileMapper;

    @Autowired
    private MiTACServiceImpl miTACService;

    /**
     * 查询保存上傳文件的信息
     *
     * @param asnNo 保存上傳文件的信息主键
     * @return 保存上傳文件的信息
     */
    @Override
    public DataFile selectDataFileByAsnNo(String asnNo)
    {
        return dataFileMapper.selectDataFileByAsnNo(asnNo);
    }

    /**
     * 查询保存上傳文件的信息列表
     *
     * @param dataFile 保存上傳文件的信息
     * @return 保存上傳文件的信息
     */
    @Override
    public List<DataFile> selectDataFileList(DataFile dataFile)
    {
        return dataFileMapper.selectDataFileList(dataFile);
    }

    /**
     * 新增保存上傳文件的信息
     *
     * @param dataFile 保存上傳文件的信息
     * @return 结果
     */
    @Override
    public int insertDataFile(DataFile dataFile)
    {
        return dataFileMapper.insertDataFile(dataFile);
    }

    /**
     * 修改保存上傳文件的信息
     *
     * @param dataFile 保存上傳文件的信息
     * @return 结果
     */
    @Override
    public int updateDataFile(DataFile dataFile)
    {
        return dataFileMapper.updateDataFile(dataFile);
    }

    /**
     * 批量删除保存上傳文件的信息
     *
     * @param asnNos 需要删除的保存上傳文件的信息主键
     * @return 结果
     */
    @Override
    public int deleteDataFileByAsnNos(String[] asnNos)
    {
        return dataFileMapper.deleteDataFileByAsnNos(asnNos);
    }

    /**
     * 删除保存上傳文件的信息信息
     *
     * @param asnNo 保存上傳文件的信息主键
     * @return 结果
     */
    @Override
    public int deleteDataFileByAsnNo(String asnNo)
    {
        return dataFileMapper.deleteDataFileByAsnNo(asnNo);
    }

    /** 文件保存根路径 */
    private static final String FILE_SAVE_PATH = "D:/QualityDataFile/";

    @Override
    @Transactional
    public void receiveFile(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new RuntimeException("上传文件不能为空");
        }

        StringBuilder resultMsg = new StringBuilder();

        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }

            try {
                log.info("开始处理文件: {}", file.getOriginalFilename());

                // 校验文件类型
                String extension = FilenameUtils.getExtension(file.getOriginalFilename());
                boolean isExcelFile = "xls".equalsIgnoreCase(extension) || "xlsx".equalsIgnoreCase(extension);
                if (!isExcelFile) {
                    throw new RuntimeException("文件格式不正确，请上传Excel文件");
                }

                // 按照年月创建目录
                java.text.SimpleDateFormat dirFormat = new java.text.SimpleDateFormat("yyyy/MM");
                String datePath = dirFormat.format(new java.util.Date());
                String dirPath = FILE_SAVE_PATH + datePath;

                // 创建目录
                File dir = new File(dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();
                }

                // 生成新的文件名
                String newFilename = UUID.randomUUID().toString() + "." + extension;
                String filePath = dirPath + "/" + newFilename;

                // 保存文件到本地
                File dest = new File(filePath);
                file.transferTo(dest);

                // 从本地文件解析Excel
                MiTACQualityReportRequest request = ExcelParser.parseExcelFromFile(dest);
                log.info("解析Excel文件成功: {}", file.getOriginalFilename());

                // 发送质检报告到MiTAC
                MiTACQualityReportResponse response = miTACService.sendQualityReport(request);
                log.info("发送质检报告响应: {}", response.getMsg());

                // 保存文件信息到数据库
                DataFile dataFile = new DataFile();
                dataFile.setAsnNo(request.getAsnNo());
                dataFile.setPartNo(request.getPartNo());
                dataFile.setMakerPartNo(request.getMakerPartNo());
                // 注意：DataFile类中没有filePath字段，如果需要保存文件路径，请在DataFile类中添加该字段

                // 检查是否已存在
                DataFile existingFile = dataFileMapper.selectDataFileByAsnNo(request.getAsnNo());
                if (existingFile != null) {
                    log.info("更新文件信息: {}", request.getAsnNo());
                    dataFileMapper.updateDataFile(dataFile);
                } else {
                    log.info("插入文件信息: {}", request.getAsnNo());
                    dataFileMapper.insertDataFile(dataFile);
                }

                // 添加处理结果
                resultMsg.append("文件 ").append(file.getOriginalFilename())
                        .append(" 处理结果: ").append(response.getMsg()).append("\n");

            } catch (Exception e) {
                log.error("处理文件异常: {}", file.getOriginalFilename(), e);
                resultMsg.append("文件 ").append(file.getOriginalFilename())
                        .append(" 处理失败: ").append(e.getMessage()).append("\n");
                throw new RuntimeException(resultMsg.toString());
            }
        }

        // 如果有错误信息，抛出异常
        if (resultMsg.toString().contains("失败")) {
            throw new RuntimeException(resultMsg.toString());
        }
    }
}
