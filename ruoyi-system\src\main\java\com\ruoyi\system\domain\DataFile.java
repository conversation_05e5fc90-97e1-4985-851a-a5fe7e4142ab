package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 保存上傳文件的信息对象 data_file
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public class DataFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 送貨單號 */
    @Excel(name = "送貨單號")
    private String asnNo;

    /** 客戶料號 */
    @Excel(name = "客戶料號")
    private String partNo;

    /** Maker料號 */
    @Excel(name = "Maker料號")
    private String makerPartNo;

    public void setAsnNo(String asnNo) 
    {
        this.asnNo = asnNo;
    }

    public String getAsnNo() 
    {
        return asnNo;
    }

    public void setPartNo(String partNo) 
    {
        this.partNo = partNo;
    }

    public String getPartNo() 
    {
        return partNo;
    }

    public void setMakerPartNo(String makerPartNo) 
    {
        this.makerPartNo = makerPartNo;
    }

    public String getMakerPartNo() 
    {
        return makerPartNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("asnNo", getAsnNo())
            .append("partNo", getPartNo())
            .append("makerPartNo", getMakerPartNo())
            .toString();
    }
}
