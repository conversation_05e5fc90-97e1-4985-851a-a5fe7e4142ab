<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeCategoryProductMapper">

    <resultMap type="AiDataExchangeCategoryProduct" id="AiDataExchangeCategoryProductResult">
        <result property="aiDataExchangeCategoryId"    column="ai_data_exchange_category_id"    />
        <result property="aiDataExchangeProductId"    column="ai_data_exchange_product_id"    />
    </resultMap>

    <sql id="selectAiDataExchangeCategoryProductVo">
        select ai_data_exchange_category_id, ai_data_exchange_product_id from ai_data_exchange_category_product
    </sql>

    <select id="selectAiDataExchangeCategoryProductList" parameterType="AiDataExchangeCategoryProduct" resultMap="AiDataExchangeCategoryProductResult">
        <include refid="selectAiDataExchangeCategoryProductVo"/>
        <where>
            <if test="aiDataExchangeCategoryId != null "> and ai_data_exchange_category_id = #{aiDataExchangeCategoryId}</if>
            <if test="aiDataExchangeProductId != null "> and ai_data_exchange_product_id = #{aiDataExchangeProductId}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeCategoryProductByIds" resultMap="AiDataExchangeCategoryProductResult">
        <include refid="selectAiDataExchangeCategoryProductVo"/>
        where ai_data_exchange_category_id = #{aiDataExchangeCategoryId} and ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </select>

    <insert id="insertAiDataExchangeCategoryProduct" parameterType="AiDataExchangeCategoryProduct">
        insert into ai_data_exchange_category_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeCategoryId != null">ai_data_exchange_category_id,</if>
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeCategoryId != null">#{aiDataExchangeCategoryId},</if>
            <if test="aiDataExchangeProductId != null">#{aiDataExchangeProductId},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeCategoryProduct" parameterType="AiDataExchangeCategoryProduct">
        update ai_data_exchange_category_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="aiDataExchangeCategoryId != null">ai_data_exchange_category_id = #{aiDataExchangeCategoryId},</if>
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id = #{aiDataExchangeProductId},</if>
        </trim>
        where ai_data_exchange_category_id = #{aiDataExchangeCategoryId} and ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </update>

    <delete id="deleteAiDataExchangeCategoryProductByIds">
        delete from ai_data_exchange_category_product where ai_data_exchange_category_id = #{aiDataExchangeCategoryId} and ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </delete>

    <delete id="deleteAiDataExchangeCategoryProductByCategoryIds" parameterType="String">
        delete from ai_data_exchange_category_product where ai_data_exchange_category_id in
        <foreach item="aiDataExchangeCategoryId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeCategoryId}
        </foreach>
    </delete>

    <delete id="deleteAiDataExchangeCategoryProductByProductIds" parameterType="String">
        delete from ai_data_exchange_category_product where ai_data_exchange_product_id in
        <foreach item="aiDataExchangeProductId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeProductId}
        </foreach>
    </delete>

    <resultMap type="AiDataExchangeProduct" id="AiDataExchangeProductResult">
        <result property="aiDataExchangeProductId"    column="ai_data_exchange_product_id"    />
        <result property="productName"    column="product_name"    />
    </resultMap>

    <select id="selectProductsByCategoryId" parameterType="Long" resultMap="AiDataExchangeProductResult">
        select p.ai_data_exchange_product_id, p.product_name
        from ai_data_exchange_product p
        inner join ai_data_exchange_category_product cp on p.ai_data_exchange_product_id = cp.ai_data_exchange_product_id
        where cp.ai_data_exchange_category_id = #{categoryId}
    </select>
</mapper>
