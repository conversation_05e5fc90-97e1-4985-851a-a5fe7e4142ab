package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;

/**
 * AI数据交换的maker料号信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IAiDataExchangeMakerPartNoService 
{
    /**
     * 查询AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号信息主键
     * @return AI数据交换的maker料号信息
     */
    public AiDataExchangeMakerPartNo selectAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId);

    /**
     * 查询AI数据交换的maker料号信息列表
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return AI数据交换的maker料号信息集合
     */
    public List<AiDataExchangeMakerPartNo> selectAiDataExchangeMakerPartNoList(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo);

    /**
     * 新增AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return 结果
     */
    public int insertAiDataExchangeMakerPartNo(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo);

    /**
     * 修改AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNo AI数据交换的maker料号信息
     * @return 结果
     */
    public int updateAiDataExchangeMakerPartNo(AiDataExchangeMakerPartNo aiDataExchangeMakerPartNo);

    /**
     * 批量删除AI数据交换的maker料号信息
     * 
     * @param aiDataExchangeMakerPartNoIds 需要删除的AI数据交换的maker料号信息主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds);

    /**
     * 删除AI数据交换的maker料号信息信息
     * 
     * @param aiDataExchangeMakerPartNoId AI数据交换的maker料号信息主键
     * @return 结果
     */
    public int deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId(Long aiDataExchangeMakerPartNoId);
}
