package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DataFile;
import com.ruoyi.system.service.IDataFileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 保存上傳文件的信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@RestController
@RequestMapping("/system/quality")
public class QualityDataFileController extends BaseController
{
    @Autowired
    private IDataFileService dataFileService;

    /**
     * 查询保存上傳文件的信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:quality:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataFile dataFile)
    {
        startPage();
        List<DataFile> list = dataFileService.selectDataFileList(dataFile);
        return getDataTable(list);
    }

    /**
     * 导出保存上傳文件的信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:quality:export')")
    @Log(title = "保存上傳文件的信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataFile dataFile)
    {
        List<DataFile> list = dataFileService.selectDataFileList(dataFile);
        ExcelUtil<DataFile> util = new ExcelUtil<DataFile>(DataFile.class);
        util.exportExcel(response, list, "保存上傳文件的信息数据");
    }

    /**
     * 获取保存上傳文件的信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quality:query')")
    @GetMapping(value = "/{asnNo}")
    public AjaxResult getInfo(@PathVariable("asnNo") String asnNo)
    {
        return success(dataFileService.selectDataFileByAsnNo(asnNo));
    }

    /**
     * 新增保存上傳文件的信息
     */
    @PreAuthorize("@ss.hasPermi('system:quality:add')")
    @Log(title = "保存上傳文件的信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataFile dataFile)
    {
        return toAjax(dataFileService.insertDataFile(dataFile));
    }

    /**
     * 修改保存上傳文件的信息
     */
    @PreAuthorize("@ss.hasPermi('system:quality:edit')")
    @Log(title = "保存上傳文件的信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataFile dataFile)
    {
        return toAjax(dataFileService.updateDataFile(dataFile));
    }

    /**
     * 删除保存上傳文件的信息
     */
    @PreAuthorize("@ss.hasPermi('system:quality:remove')")
    @Log(title = "保存上傳文件的信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{asnNos}")
    public AjaxResult remove(@PathVariable String[] asnNos)
    {
        return toAjax(dataFileService.deleteDataFileByAsnNos(asnNos));
    }

    /**
     * 上传文件
     */
    @PreAuthorize("@ss.hasPermi('system:quality:add')")
    @Log(title = "文件上传", businessType = BusinessType.INSERT)
    @PostMapping("/post")
    public AjaxResult uploadFiles(@RequestParam("files") MultipartFile[] files)
    {
        try {
            dataFileService.receiveFile(files);
            return AjaxResult.success("文件上传成功");
        } catch (Exception e) {
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

}
