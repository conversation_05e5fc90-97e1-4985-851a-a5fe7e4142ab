package com.ruoyi.system.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.domain.AiDataExchangeCategory;
import com.ruoyi.system.domain.AiDataExchangeCategoryProduct;
import com.ruoyi.system.domain.AiDataExchangeMainInfo;
import com.ruoyi.system.domain.AiDataExchangeMakerPartNo;
import com.ruoyi.system.domain.AiDataExchangeProduct;
import com.ruoyi.system.domain.AiDataExchangeProductMakerPartNo;
import com.ruoyi.system.service.IAiDataExchangeCategoryProductService;
import com.ruoyi.system.service.IAiDataExchangeCategoryService;
import com.ruoyi.system.service.IAiDataExchangeMainInfoService;
import com.ruoyi.system.service.IAiDataExchangeMakerPartNoService;
import com.ruoyi.system.service.IAiDataExchangeProductMakerPartNoService;
import com.ruoyi.system.service.IAiDataExchangeProductService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI数据交换Controller
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Api(tags = "AI数据交换管理")
@RestController
@RequestMapping("/system/aidataexchange")
public class AiDataExchangeMainInfoController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AiDataExchangeMainInfoController.class);
    @Autowired
    private IAiDataExchangeMainInfoService aiDataExchangeMainInfoService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IAiDataExchangeCategoryService aiDataExchangeCategoryService;

    @Autowired
    private IAiDataExchangeCategoryProductService aiDataExchangeCategoryProductService;

    @Autowired
    private IAiDataExchangeProductMakerPartNoService aiDataExchangeProductMakerPartNoService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IAiDataExchangeProductService aiDataExchangeProductService;

    @Autowired
    private IAiDataExchangeMakerPartNoService aiDataExchangeMakerPartNoService;

    /**
     * 查询AI数据交换列表
     */
    @ApiOperation(value = "查询AI数据交换列表", notes = "获取AI数据交换的分页列表数据")
    @ApiImplicitParam(name = "aiDataExchangeMainInfo", value = "查询参数", dataTypeClass = AiDataExchangeMainInfo.class)
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @PostMapping("/list")
    public TableDataInfo list(AiDataExchangeMainInfo aiDataExchangeMainInfo)
    {
        startPage();
        List<AiDataExchangeMainInfo> list = aiDataExchangeMainInfoService.selectAiDataExchangeMainInfoList(aiDataExchangeMainInfo);
        return getDataTable(list);
    }

//    /**
//     * 导出AI数据交换列表(暂时废弃)
//     */
//    @ApiOperation(value = "导出AI数据交换列表", notes = "导出AI数据交换数据到Excel文件")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "response", value = "响应对象", dataTypeClass = HttpServletResponse.class),
//        @ApiImplicitParam(name = "aiDataExchangeMainInfo", value = "查询参数", dataTypeClass = AiDataExchangeMainInfo.class)
//    })
//    @PreAuthorize("@ss.hasPermi('system:aidataexchange:export')")
//    @Log(title = "AI数据交换", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, AiDataExchangeMainInfo aiDataExchangeMainInfo)
//    {
//        List<AiDataExchangeMainInfo> list = aiDataExchangeMainInfoService.selectAiDataExchangeMainInfoList(aiDataExchangeMainInfo);
//        ExcelUtil<AiDataExchangeMainInfo> util = new ExcelUtil<AiDataExchangeMainInfo>(AiDataExchangeMainInfo.class);
//        util.exportExcel(response, list, "AI数据交换数据");
//    }

//    /**
//     * 获取AI数据交换详细信息（暂时废弃）
//     */
//    @ApiOperation(value = "获取AI数据交换详情", notes = "根据ID获取AI数据交换的详细信息")
//    @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path")
//    @PreAuthorize("@ss.hasPermi('system:aidataexchange:query')")
//    @GetMapping(value = "/{aiDataExchangeMainInfoId}")
//    public AjaxResult getInfo(@PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId)
//    {
//        return success(aiDataExchangeMainInfoService.selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(aiDataExchangeMainInfoId));
//    }

//    /**
//     * 新增AI数据交换（暂时废弃）
//     */
//    @ApiOperation(value = "新增AI数据交换", notes = "新增AI数据交换信息")
//    @ApiImplicitParam(name = "aiDataExchangeMainInfo", value = "AI数据交换信息", required = true, dataTypeClass = AiDataExchangeMainInfo.class)
//    @PreAuthorize("@ss.hasPermi('system:aidataexchange:add')")
//    @Log(title = "AI数据交换", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody AiDataExchangeMainInfo aiDataExchangeMainInfo)
//    {
//        return toAjax(aiDataExchangeMainInfoService.insertAiDataExchangeMainInfo(aiDataExchangeMainInfo));
//    }

//    /**
//     * 修改AI数据交换（暂时废弃）
//     */
//    @ApiOperation(value = "修改AI数据交换", notes = "修改AI数据交换信息")
//    @ApiImplicitParam(name = "aiDataExchangeMainInfo", value = "AI数据交换信息", required = true, dataTypeClass = AiDataExchangeMainInfo.class)
//    @PreAuthorize("@ss.hasPermi('system:aidataexchange:edit')")
//    @Log(title = "AI数据交换", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody AiDataExchangeMainInfo aiDataExchangeMainInfo)
//    {
//        return toAjax(aiDataExchangeMainInfoService.updateAiDataExchangeMainInfo(aiDataExchangeMainInfo));
//    }

//    /**
//     * 删除AI数据交换（暂时废弃）
//     */
//    @ApiOperation(value = "删除AI数据交换", notes = "根据ID数组批量删除AI数据交换信息")
//    @ApiImplicitParam(name = "aiDataExchangeMainInfoIds", value = "AI数据交换ID数组", required = true, dataTypeClass = Long[].class, paramType = "path")
//    @PreAuthorize("@ss.hasPermi('system:aidataexchange:remove')")
//    @Log(title = "AI数据交换", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{aiDataExchangeMainInfoIds}")
//    public AjaxResult remove(@PathVariable Long[] aiDataExchangeMainInfoIds)
//    {
//        return toAjax(aiDataExchangeMainInfoService.deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds(aiDataExchangeMainInfoIds));
//    }

    /**
     * 上传Excel文件
     */
    @ApiOperation(value = "上传Excel文件", notes = "上传并解析Excel文件数据")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "Excel文件", required = true, dataTypeClass = MultipartFile.class),
        @ApiImplicitParam(name = "deptId", value = "课别ID", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "factoryId", value = "厂区ID", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "idsId", value = "产品处IDS ID", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "functionId", value = "机能ID", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:upload')")
    @Log(title = "AI数据交换", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file,
                                @RequestParam("deptId") Long deptId,
                                @RequestParam("factoryId") Long factoryId,
                                @RequestParam("idsId") Long idsId,
                                @RequestParam("functionId") Long functionId)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();

        // 调用Service层的上传文件方法，直接返回包含aiDataExchangeMainInfo信息的结果
        return aiDataExchangeMainInfoService.uploadFile(file, deptId, factoryId, idsId, functionId, userId);
    }

    /**
     * 查询厂区信息
     */
    @ApiOperation(value = "查询厂区信息", notes = "查询所有厂区信息（parent_id=0的部门）")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/factory/list")
    public AjaxResult listFactories()
    {
        List<SysDept> list = sysDeptService.selectDeptListByParentId(0L);
        return success(list);
    }

    /**
     * 根据厂区ID查询SBU信息
     */
    @ApiOperation(value = "查询SBU信息", notes = "根据厂区ID查询所有SBU信息")
    @ApiImplicitParam(name = "factoryId", value = "厂区ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/sbu/list/{factoryId}")
    public AjaxResult listSbusByFactoryId(@PathVariable("factoryId") Long factoryId)
    {
        List<SysDept> list = sysDeptService.selectDeptListByParentId(factoryId);
        return success(list);
    }

    /**
     * 根据SBU ID查询机能信息
     */
    @ApiOperation(value = "查询机能信息", notes = "根据SBU ID查询所有机能信息")
    @ApiImplicitParam(name = "sbuId", value = "SBU ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/function/list/{sbuId}")
    public AjaxResult listFunctionsBySbuId(@PathVariable("sbuId") Long sbuId)
    {
        List<SysDept> list = sysDeptService.selectDeptListByParentId(sbuId);
        return success(list);
    }

    /**
     * 根据机能ID查询课别信息
     */
    @ApiOperation(value = "查询课别信息", notes = "根据机能ID查询所有课别信息")
    @ApiImplicitParam(name = "functionId", value = "机能ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/class/list/{functionId}")
    public AjaxResult listClassesByFunctionId(@PathVariable("functionId") Long functionId)
    {
        List<SysDept> list = sysDeptService.selectDeptListByParentId(functionId);
        return success(list);
    }

    /**
     * 查询所有产品类别信息
     */
    @ApiOperation(value = "查询产品类别信息", notes = "查询所有产品类别信息")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/category/list")
    public AjaxResult listCategories()
    {
        AiDataExchangeCategory category = new AiDataExchangeCategory();
        List<AiDataExchangeCategory> list = aiDataExchangeCategoryService.selectAiDataExchangeCategoryList(category);
        return success(list);
    }

    /**
     * 根据产品类别ID查询产品信息
     */
    @ApiOperation(value = "查询产品信息", notes = "根据产品类别ID查询产品信息")
    @ApiImplicitParam(name = "categoryId", value = "产品类别ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/product/list/{categoryId}")
    public AjaxResult listProductsByCategoryId(@PathVariable("categoryId") Long categoryId)
    {
        List<AiDataExchangeProduct> list = aiDataExchangeCategoryProductService.selectProductsByCategoryId(categoryId);
        return success(list);
    }

    /**
     * 根据产品ID查询maker料号信息
     */
    @ApiOperation(value = "查询maker料号信息", notes = "根据产品ID查询maker料号信息")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/makerpartno/list/{productId}")
    public AjaxResult listMakerPartNosByProductId(@PathVariable("productId") Long productId)
    {
        List<AiDataExchangeMakerPartNo> list = aiDataExchangeProductMakerPartNoService.selectMakerPartNosByProductId(productId);
        return success(list);
    }

    /**
     * 下载文件
     */
    @ApiOperation(value = "下载文件", notes = "根据AI数据交换ID下载对应的文件")
    @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:download')")
    @Log(title = "AI数据交换", businessType = BusinessType.EXPORT)
    @GetMapping("/download/{aiDataExchangeMainInfoId}")
    public void downloadFile(@PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId, HttpServletResponse response)
    {
        try
        {
            aiDataExchangeMainInfoService.downloadFile(aiDataExchangeMainInfoId, response);
        }
        catch (Exception e)
        {
            log.error("下载文件失败：", e);
        }
    }

    /**
     * 复核人复核
     */
    @ApiOperation(value = "复核人复核", notes = "复核人复核操作，将状态修改为待核定")
    @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:check')")
    @Log(title = "AI数据交换", businessType = BusinessType.UPDATE)
    @PutMapping("/check/{aiDataExchangeMainInfoId}")
    public AjaxResult check(@PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId)
    {
        // 调用Service层的复核方法，包含发送邮件逻辑
        return aiDataExchangeMainInfoService.checkRecord(aiDataExchangeMainInfoId);
    }

    /**
     * 核定人核定
     */
    @ApiOperation(value = "核定人核定", notes = "核定人核定操作，将状态修改为已完成并发送质检报告到MiTAC")
    @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:approve')")
    @Log(title = "AI数据交换", businessType = BusinessType.UPDATE)
    @PutMapping("/approve/{aiDataExchangeMainInfoId}")
    public AjaxResult approve(@PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId)
    {
        // 调用service层的核定并发送质检报告方法
        return aiDataExchangeMainInfoService.approveAndSendReport(aiDataExchangeMainInfoId);
    }

    /**
     * 驳回记录
     */
    @ApiOperation(value = "驳回记录", notes = "驳回记录操作，将状态修改为已驳回并记录驳回原因")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path"),
        @ApiImplicitParam(name = "rejectReason", value = "驳回原因", required = true, dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:reject')")
    @Log(title = "AI数据交换", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{aiDataExchangeMainInfoId}")
    public AjaxResult reject(@PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId, @RequestParam("rejectReason") String rejectReason)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();

        // 调用service层的驳回方法
        return aiDataExchangeMainInfoService.rejectRecord(aiDataExchangeMainInfoId, rejectReason, userId);
    }

    /**
     * 重新提交文件
     */
    @ApiOperation(value = "重新提交文件", notes = "重新提交文件，替换已驳回记录的文件")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "Excel文件", required = true, dataTypeClass = MultipartFile.class),
        @ApiImplicitParam(name = "aiDataExchangeMainInfoId", value = "AI数据交换ID", required = true, dataTypeClass = Long.class, paramType = "path")
    })
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:upload')")
    @Log(title = "AI数据交换", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/resubmit/{aiDataExchangeMainInfoId}", consumes = "multipart/form-data")
    public AjaxResult resubmit(@RequestParam("file") MultipartFile file, @PathVariable("aiDataExchangeMainInfoId") Long aiDataExchangeMainInfoId)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();

        // 调用service层的重新提交文件方法
        return aiDataExchangeMainInfoService.resubmitFile(file, aiDataExchangeMainInfoId, userId);
    }

    /**
     * 查询用户部门及其父部门信息
     */
    @ApiOperation(value = "查询用户部门及其父部门信息", notes = "根据用户账号查询其部门及父部门的复杂信息")
    @ApiImplicitParam(name = "userName", value = "用户账号", required = true, dataTypeClass = String.class, paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:list')")
    @GetMapping("/user/dept/{userName}")
    public AjaxResult getUserDeptInfo(@PathVariable("userName") String userName)
    {
        // 查询用户信息
        SysUser user = userService.selectUserByUserName(userName);
        if (user == null)
        {
            return AjaxResult.error("未找到该用户");
        }

        // 获取用户部门ID
        Long deptId = user.getDeptId();
        if (deptId == null)
        {
            return AjaxResult.error("该用户未分配部门");
        }

        // 查询用户部门信息
        SysDept dept = sysDeptService.selectDeptById(deptId);
        if (dept == null)
        {
            return AjaxResult.error("未找到该部门");
        }

        // 获取部门的祖级列表
        String ancestors = dept.getAncestors();
        if (StringUtils.isEmpty(ancestors))
        {
            return AjaxResult.error("部门祖级列表为空");
        }

        // 解析祖级列表，获取所有父部门ID
        String[] ancestorIds = ancestors.split(",");

        // 构建结果对象
        Map<String, Object> result = new HashMap<>();

//        // 添加用户信息
//        Map<String, Object> userInfo = new HashMap<>();
//        userInfo.put("userId", user.getUserId());
//        userInfo.put("userName", user.getUserName());
//        userInfo.put("nickName", user.getNickName());
//        result.put("user", userInfo);

        // 添加部门信息
        result.put("deptId", dept.getDeptId());
        result.put("deptName", dept.getDeptName());
        result.put("currentDeptName", dept.getDeptName()); // 明确标识用户当前部门名

        // 查询并构建祖级部门信息
        List<Map<String, Object>> ancestorList = new ArrayList<>();
        String factoryName = "";
        String idsName = "";
        String functionName = "";
        Long factoryId = null;
        Long idsId = null;
        Long functionId = null;

        for (String ancestorId : ancestorIds)
        {
            if (StringUtils.isNotEmpty(ancestorId))
            {
                Long parentId = Long.parseLong(ancestorId);
                SysDept parentDept = sysDeptService.selectDeptById(parentId);
                if (parentDept != null)
                {
                    Map<String, Object> parentInfo = new HashMap<>();
                    parentInfo.put("deptId", parentDept.getDeptId());
                    parentInfo.put("deptName", parentDept.getDeptName());
                    ancestorList.add(parentInfo);

                    // 根据层级关系确定厂区名、产品处IDS名和机能名及其ID
                    if (parentDept.getParentId() == 0)
                    {
                        // 厂区（parent_id=0）
                        factoryName = parentDept.getDeptName();
                        factoryId = parentDept.getDeptId();
                    }
                    else if (factoryName.length() > 0 && idsName.length() == 0)
                    {
                        // 产品处IDS（第二层）
                        idsName = parentDept.getDeptName();
                        idsId = parentDept.getDeptId();
                    }
                    else if (factoryName.length() > 0 && idsName.length() > 0 && functionName.length() == 0)
                    {
                        // 机能（第三层）
                        functionName = parentDept.getDeptName();
                        functionId = parentDept.getDeptId();
                    }
                }
            }
        }

        // // 构建包含厂区名、产品处IDS名和机能名的ancestors字段
        // StringBuilder customAncestors = new StringBuilder();
        // customAncestors.append("0,");
        // if (StringUtils.isNotEmpty(factoryName))
        // {
        //     customAncestors.append(factoryName).append(",");
        // }
        // if (StringUtils.isNotEmpty(idsName))
        // {
        //     customAncestors.append(idsName).append(",");
        // }
        // if (StringUtils.isNotEmpty(functionName))
        // {
        //     customAncestors.append(functionName);
        // }

//        result.put("ancestors", customAncestors.toString());
        // result.put("ancestorList", ancestorList);
        result.put("factoryName", factoryName);
        result.put("factoryId", factoryId);
        result.put("idsName", idsName);
        result.put("idsId", idsId);
        result.put("functionName", functionName);
        result.put("functionId", functionId);

        return AjaxResult.success(result);
    }

    /**
     * 添加产品和maker料号关联
     */
    @ApiOperation(value = "添加产品和maker料号关联", notes = "接收分类ID、产品名和maker料号，建立相关关联关系")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "aiDataExchangeCategoryId", value = "分类ID", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "productName", value = "产品名", required = true, dataTypeClass = String.class),
        @ApiImplicitParam(name = "makerPartNo", value = "maker料号", required = true, dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermi('system:aidataexchange:add')")
    @Log(title = "AI数据交换", businessType = BusinessType.INSERT)
    @PostMapping("/addProductMakerPartNoRelation")
    public AjaxResult addProductMakerPartNoRelation(@RequestParam("aiDataExchangeCategoryId") Long aiDataExchangeCategoryId,
                                                   @RequestParam("productName") String productName,
                                                   @RequestParam("makerPartNo") String makerPartNo)
    {
        try {
            // 步骤1：将产品名去掉空格并将所有字母都转为大写
            String processedProductName = productName.replaceAll("\\s+", "").toUpperCase();

            // 步骤2：查询产品名是否存在ai_data_exchange_product表中，存在则记录其ID，不存在则新增并记录ID
            AiDataExchangeProduct productQuery = new AiDataExchangeProduct();
            productQuery.setProductName(processedProductName);
            List<AiDataExchangeProduct> existingProducts = aiDataExchangeProductService.selectAiDataExchangeProductList(productQuery);

            Long productId;
            if (existingProducts != null && !existingProducts.isEmpty()) {
                // 产品已存在，获取其ID
                productId = existingProducts.get(0).getAiDataExchangeProductId();
            } else {
                // 产品不存在，新增产品
                AiDataExchangeProduct newProduct = new AiDataExchangeProduct();
                newProduct.setProductName(processedProductName);
                int insertResult = aiDataExchangeProductService.insertAiDataExchangeProduct(newProduct);
                if (insertResult <= 0) {
                    return AjaxResult.error("新增产品失败");
                }
                productId = newProduct.getAiDataExchangeProductId();
            }

            // 步骤3：将分类ID和步骤2记录的ID结果存入ai_data_exchange_category_product表中
            // 检查关联是否已存在，避免重复插入
            AiDataExchangeCategoryProduct existingCategoryProduct = aiDataExchangeCategoryProductService.selectAiDataExchangeCategoryProductByIds(aiDataExchangeCategoryId, productId);
            if (existingCategoryProduct == null) {
                AiDataExchangeCategoryProduct categoryProduct = new AiDataExchangeCategoryProduct();
                categoryProduct.setAiDataExchangeCategoryId(aiDataExchangeCategoryId);
                categoryProduct.setAiDataExchangeProductId(productId);
                int insertCategoryProductResult = aiDataExchangeCategoryProductService.insertAiDataExchangeCategoryProduct(categoryProduct);
                if (insertCategoryProductResult <= 0) {
                    return AjaxResult.error("新增产品类别关联失败");
                }
            }

            // 步骤4：查询maker料号是否存在ai_data_exchange_maker_part_no表中，存在则记录其ID，不存在则新增并记录ID
             String processedMakerPartNo = makerPartNo.replaceAll("\\s+", "").toUpperCase();
//            String processedMakerPartNo = makerPartNo;//暂不处理大小写和空格
            AiDataExchangeMakerPartNo makerPartNoQuery = new AiDataExchangeMakerPartNo();
            makerPartNoQuery.setMakerPartNo(processedMakerPartNo);
            List<AiDataExchangeMakerPartNo> existingMakerPartNos = aiDataExchangeMakerPartNoService.selectAiDataExchangeMakerPartNoList(makerPartNoQuery);

            Long makerPartNoId;
            if (existingMakerPartNos != null && !existingMakerPartNos.isEmpty()) {
                // maker料号已存在，获取其ID
                makerPartNoId = existingMakerPartNos.get(0).getAiDataExchangeMakerPartNoId();
            } else {
                // maker料号不存在，新增maker料号
                AiDataExchangeMakerPartNo newMakerPartNo = new AiDataExchangeMakerPartNo();
                newMakerPartNo.setMakerPartNo(processedMakerPartNo);
                int insertMakerPartNoResult = aiDataExchangeMakerPartNoService.insertAiDataExchangeMakerPartNo(newMakerPartNo);
                if (insertMakerPartNoResult <= 0) {
                    return AjaxResult.error("新增maker料号失败");
                }
                makerPartNoId = newMakerPartNo.getAiDataExchangeMakerPartNoId();
            }

            // 步骤5：将步骤2记录的ID结果和步骤4记录的ID结果存入ai_data_exchange_product_maker_part_no表中
            // 检查关联是否已存在，避免重复插入
            AiDataExchangeProductMakerPartNo existingProductMakerPartNo = aiDataExchangeProductMakerPartNoService.selectAiDataExchangeProductMakerPartNoByIds(productId, makerPartNoId);
            if (existingProductMakerPartNo == null) {
                AiDataExchangeProductMakerPartNo productMakerPartNo = new AiDataExchangeProductMakerPartNo();
                productMakerPartNo.setAiDataExchangeProductId(productId);
                productMakerPartNo.setAiDataExchangeMakerPartNoId(makerPartNoId);
                int insertProductMakerPartNoResult = aiDataExchangeProductMakerPartNoService.insertAiDataExchangeProductMakerPartNo(productMakerPartNo);
                if (insertProductMakerPartNoResult <= 0) {
                    return AjaxResult.error("新增产品与maker料号关联失败");
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("productId", productId);
            result.put("productName", processedProductName);
            result.put("makerPartNoId", makerPartNoId);
            result.put("makerPartNo", processedMakerPartNo);
            result.put("categoryId", aiDataExchangeCategoryId);

            return AjaxResult.success("添加产品和maker料号关联成功", result);

        } catch (Exception e) {
            log.error("添加产品和maker料号关联失败", e);
            return AjaxResult.error("添加产品和maker料号关联失败：" + e.getMessage());
        }
    }
}
