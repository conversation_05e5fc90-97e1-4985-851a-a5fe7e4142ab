package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AI数据交换的产品信息对象 ai_data_exchange_product
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public class AiDataExchangeProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** AI数据交换的产品ID */
    private Long aiDataExchangeProductId;

    /** 产品名 */
    @Excel(name = "产品名")
    private String productName;

    public void setAiDataExchangeProductId(Long aiDataExchangeProductId) 
    {
        this.aiDataExchangeProductId = aiDataExchangeProductId;
    }

    public Long getAiDataExchangeProductId() 
    {
        return aiDataExchangeProductId;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("aiDataExchangeProductId", getAiDataExchangeProductId())
            .append("productName", getProductName())
            .toString();
    }
}
