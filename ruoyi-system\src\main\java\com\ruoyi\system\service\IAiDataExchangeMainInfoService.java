package com.ruoyi.system.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.AiDataExchangeMainInfo;

/**
 * AI数据交换Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IAiDataExchangeMainInfoService
{
    /**
     * 查询AI数据交换
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return AI数据交换
     */
    public AiDataExchangeMainInfo selectAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId);

    /**
     * 查询AI数据交换列表
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return AI数据交换集合
     */
    public List<AiDataExchangeMainInfo> selectAiDataExchangeMainInfoList(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 新增AI数据交换
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    public int insertAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 修改AI数据交换
     *
     * @param aiDataExchangeMainInfo AI数据交换
     * @return 结果
     */
    public int updateAiDataExchangeMainInfo(AiDataExchangeMainInfo aiDataExchangeMainInfo);

    /**
     * 批量删除AI数据交换
     *
     * @param aiDataExchangeMainInfoIds 需要删除的AI数据交换主键集合
     * @return 结果
     */
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoIds(Long[] aiDataExchangeMainInfoIds);

    /**
     * 删除AI数据交换信息
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 结果
     */
    public int deleteAiDataExchangeMainInfoByAiDataExchangeMainInfoId(Long aiDataExchangeMainInfoId);

    /**
     * 上传Excel文件
     *
     * @param file 上传的文件
     * @param deptId 课别ID
     * @param factoryId 厂区ID
     * @param idsId 产品处IDS ID
     * @param functionId 机能ID
     * @param userId 用户ID
     * @return 处理结果
     */
    public AjaxResult uploadFile(MultipartFile file, Long deptId, Long factoryId, Long idsId, Long functionId, Long userId);

    /**
     * 下载文件
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param response HTTP响应对象
     */
    public void downloadFile(Long aiDataExchangeMainInfoId, HttpServletResponse response);

    /**
     * 核定并发送质检报告到MiTAC
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 处理结果
     */
    public AjaxResult approveAndSendReport(Long aiDataExchangeMainInfoId);

    /**
     * 驳回记录
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param rejectReason 驳回原因
     * @param userId 驳回人ID
     * @return 处理结果
     */
    public AjaxResult rejectRecord(Long aiDataExchangeMainInfoId, String rejectReason, Long userId);

    /**
     * 重新提交文件
     *
     * @param file 上传的文件
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @param userId 用户ID
     * @return 处理结果
     */
    public AjaxResult resubmitFile(MultipartFile file, Long aiDataExchangeMainInfoId, Long userId);

    /**
     * 复核人复核操作
     *
     * @param aiDataExchangeMainInfoId AI数据交换主键
     * @return 处理结果
     */
    public AjaxResult checkRecord(Long aiDataExchangeMainInfoId);
}
