package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AiDataExchangeApprovalMappingMapper;
import com.ruoyi.system.domain.AiDataExchangeApprovalMapping;
import com.ruoyi.system.service.IAiDataExchangeApprovalMappingService;

/**
 * 签核人员映射Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class AiDataExchangeApprovalMappingServiceImpl implements IAiDataExchangeApprovalMappingService 
{
    @Autowired
    private AiDataExchangeApprovalMappingMapper aiDataExchangeApprovalMappingMapper;

    /**
     * 查询签核人员映射
     * 
     * @param aiDataExchangeMakerPartNoId 签核人员映射主键
     * @param userId 签核人员ID
     * @return 签核人员映射
     */
    @Override
    public AiDataExchangeApprovalMapping selectAiDataExchangeApprovalMappingByIds(Long aiDataExchangeMakerPartNoId, Long userId)
    {
        return aiDataExchangeApprovalMappingMapper.selectAiDataExchangeApprovalMappingByIds(aiDataExchangeMakerPartNoId, userId);
    }

    /**
     * 查询签核人员映射列表
     * 
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 签核人员映射
     */
    @Override
    public List<AiDataExchangeApprovalMapping> selectAiDataExchangeApprovalMappingList(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping)
    {
        return aiDataExchangeApprovalMappingMapper.selectAiDataExchangeApprovalMappingList(aiDataExchangeApprovalMapping);
    }

    /**
     * 新增签核人员映射
     * 
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 结果
     */
    @Override
    public int insertAiDataExchangeApprovalMapping(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping)
    {
        return aiDataExchangeApprovalMappingMapper.insertAiDataExchangeApprovalMapping(aiDataExchangeApprovalMapping);
    }

    /**
     * 修改签核人员映射
     * 
     * @param aiDataExchangeApprovalMapping 签核人员映射
     * @return 结果
     */
    @Override
    public int updateAiDataExchangeApprovalMapping(AiDataExchangeApprovalMapping aiDataExchangeApprovalMapping)
    {
        return aiDataExchangeApprovalMappingMapper.updateAiDataExchangeApprovalMapping(aiDataExchangeApprovalMapping);
    }

    /**
     * 批量删除签核人员映射
     * 
     * @param aiDataExchangeMakerPartNoIds 需要删除的签核人员映射主键
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeApprovalMappingByMakerPartNoIds(Long[] aiDataExchangeMakerPartNoIds)
    {
        return aiDataExchangeApprovalMappingMapper.deleteAiDataExchangeApprovalMappingByMakerPartNoIds(aiDataExchangeMakerPartNoIds);
    }

    /**
     * 删除签核人员映射信息
     * 
     * @param aiDataExchangeMakerPartNoId 签核人员映射主键
     * @param userId 签核人员ID
     * @return 结果
     */
    @Override
    public int deleteAiDataExchangeApprovalMappingByIds(Long aiDataExchangeMakerPartNoId, Long userId)
    {
        return aiDataExchangeApprovalMappingMapper.deleteAiDataExchangeApprovalMappingByIds(aiDataExchangeMakerPartNoId, userId);
    }
}
