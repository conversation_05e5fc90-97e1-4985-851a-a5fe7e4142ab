<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeApprovalMappingMapper">

    <resultMap type="AiDataExchangeApprovalMapping" id="AiDataExchangeApprovalMappingResult">
        <result property="aiDataExchangeMakerPartNoId"    column="ai_data_exchange_maker_part_no_id"    />
        <result property="userId"    column="user_id"    />
        <result property="approverType"    column="approver_type"    />
    </resultMap>

    <sql id="selectAiDataExchangeApprovalMappingVo">
        select ai_data_exchange_maker_part_no_id, user_id, approver_type from ai_data_exchange_approval_mapping
    </sql>

    <resultMap type="com.ruoyi.system.domain.dto.ApprovalUserDTO" id="ApprovalUserDTOResult">
        <result property="userId"    column="user_id"    />
        <result property="nickName"  column="nick_name"  />
        <result property="approverType" column="approver_type" />
    </resultMap>

    <select id="selectApprovalUsersByMakerPartNoId" parameterType="Long" resultMap="ApprovalUserDTOResult">
        select a.user_id, u.nick_name, a.approver_type
        from ai_data_exchange_approval_mapping a
        left join sys_user u on a.user_id = u.user_id
        where a.ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </select>

    <select id="selectAiDataExchangeApprovalMappingList" parameterType="AiDataExchangeApprovalMapping" resultMap="AiDataExchangeApprovalMappingResult">
        <include refid="selectAiDataExchangeApprovalMappingVo"/>
        <where>
            <if test="aiDataExchangeMakerPartNoId != null "> and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="approverType != null  and approverType != ''"> and approver_type = #{approverType}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeApprovalMappingByIds" resultMap="AiDataExchangeApprovalMappingResult">
        <include refid="selectAiDataExchangeApprovalMappingVo"/>
        where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId} and user_id = #{userId}
    </select>

    <insert id="insertAiDataExchangeApprovalMapping" parameterType="AiDataExchangeApprovalMapping">
        insert into ai_data_exchange_approval_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeMakerPartNoId != null">ai_data_exchange_maker_part_no_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="approverType != null">approver_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeMakerPartNoId != null">#{aiDataExchangeMakerPartNoId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="approverType != null">#{approverType},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeApprovalMapping" parameterType="AiDataExchangeApprovalMapping">
        update ai_data_exchange_approval_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="approverType != null">approver_type = #{approverType},</if>
        </trim>
        where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId} and user_id = #{userId}
    </update>

    <delete id="deleteAiDataExchangeApprovalMappingByIds">
        delete from ai_data_exchange_approval_mapping where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId} and user_id = #{userId}
    </delete>

    <delete id="deleteAiDataExchangeApprovalMappingByMakerPartNoIds" parameterType="String">
        delete from ai_data_exchange_approval_mapping where ai_data_exchange_maker_part_no_id in
        <foreach item="aiDataExchangeMakerPartNoId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeMakerPartNoId}
        </foreach>
    </delete>

    <delete id="deleteAiDataExchangeApprovalMappingByUserIds" parameterType="String">
        delete from ai_data_exchange_approval_mapping where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
